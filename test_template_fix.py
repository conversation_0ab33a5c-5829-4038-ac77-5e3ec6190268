#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模板修复效果
"""

from pathlib import Path
from docx_to_latex_correct import CorrectDocxToLatexConverter
import shutil

def test_template_fix():
    """测试模板修复效果"""
    print("🚀 测试模板修复效果...")
    
    # 输入文件
    input_docx = Path("实例文档/双栏案列/FLS 8565 - 原始版本.docx")
    output_dir = Path("test_template_fix")
    
    # 清理并创建输出目录
    if output_dir.exists():
        shutil.rmtree(output_dir)
    output_dir.mkdir(exist_ok=True)
    
    if not input_docx.exists():
        print(f"❌ 找不到输入文件: {input_docx}")
        return False
    
    try:
        # 创建转换器
        converter = CorrectDocxToLatexConverter(input_docx, output_dir)
        
        print(f"📄 正在转换文件: {input_docx}")
        
        # 转换为LaTeX
        tex_file = converter.convert_to_latex()
        
        if tex_file:
            print(f"✅ LaTeX文件生成成功: {tex_file}")
            
            # 检查生成的LaTeX文件中的关键设置
            with open(tex_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print("\n🔍 检查关键设置:")
            
            # 检查标题间距设置
            checks = [
                ("section标题间距", r"\\titlespacing\*\{\\section\}\{0pt\}\{15pt\}\{10pt\}", "15pt/10pt"),
                ("subsection标题间距", r"\\titlespacing\*\{\\subsection\}\{0pt\}\{15pt\}\{10pt\}", "15pt/10pt"),
                ("第一页页眉间距", r"\\setlength\{\\headsep\}\{4mm\}", "4mm"),
                ("其他页页眉间距", r"\\setlength\{\\headsep\}\{7mm\}", "7mm"),
                ("第一页顶部间距", r"\\vspace\{2\.7mm\}", "2.7mm"),
                ("articletype命令", r"\\newcommand\{\\articletype\}", "已定义"),
                ("articletitle命令", r"\\newcommand\{\\articletitle\}", "已定义"),
                ("articleabstract命令", r"\\newcommand\{\\articleabstract\}", "已定义"),
                ("supernum命令", r"\\newcommand\{\\supernum\}", "已定义")
            ]
            
            all_correct = True
            for name, pattern, expected in checks:
                import re
                if re.search(pattern, content):
                    print(f"   ✅ {name}: {expected}")
                else:
                    print(f"   ❌ {name}: 未找到 {expected}")
                    all_correct = False
            
            if all_correct:
                print("\n🎉 所有设置都符合模板要求！")
                return True
            else:
                print("\n⚠️ 发现不符合模板的设置")
                return False
        else:
            print("❌ LaTeX文件生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 转换过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    success = test_template_fix()
    if success:
        print("\n✅ 模板修复测试通过！")
    else:
        print("\n❌ 模板修复测试失败！")

#!/usr/bin/env python3
"""
创建Figure 2 - QR码和手机界面图片
"""

from PIL import Image, ImageDraw, ImageFont
import qrcode
import os

def create_figure2():
    """创建Figure 2 - QR码和手机界面的组合图片"""
    
    # 创建主画布 (800x400像素)
    width, height = 800, 400
    img = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(img)
    
    # 1. 创建QR码 (左侧)
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=8,
        border=4,
    )
    qr.add_data('https://example.com/audio-dialogues')
    qr.make(fit=True)
    
    qr_img = qr.make_image(fill_color="black", back_color="white")
    qr_img = qr_img.resize((200, 200))
    
    # 将QR码粘贴到左侧
    img.paste(qr_img, (50, 100))
    
    # 添加QR码下方的文字
    try:
        font = ImageFont.truetype("arial.ttf", 12)
    except:
        font = ImageFont.load_default()
    
    draw.text((50, 320), "Click / Scan QR code to", fill="black", font=font)
    draw.text((50, 340), "access Unit 1", fill="black", font=font)
    
    # 2. 创建手机界面模拟 (右侧)
    # 手机外框
    phone_x, phone_y = 400, 50
    phone_width, phone_height = 300, 300
    
    # 绘制手机外框
    draw.rectangle([phone_x, phone_y, phone_x + phone_width, phone_y + phone_height], 
                  outline="black", width=3)
    
    # 绘制屏幕
    screen_margin = 20
    draw.rectangle([phone_x + screen_margin, phone_y + screen_margin, 
                   phone_x + phone_width - screen_margin, phone_y + phone_height - screen_margin], 
                  fill="lightblue", outline="darkblue", width=2)
    
    # 添加标题栏
    draw.rectangle([phone_x + screen_margin, phone_y + screen_margin, 
                   phone_x + phone_width - screen_margin, phone_y + screen_margin + 40], 
                  fill="darkblue")
    
    draw.text((phone_x + 30, phone_y + 35), "Part 1", fill="white", font=font)
    draw.text((phone_x + 30, phone_y + 50), "Recommended", fill="white", font=font)
    
    # 添加内容区域
    content_y = phone_y + screen_margin + 50
    draw.text((phone_x + 30, content_y), "English Conversation Practice", fill="black", font=font)
    draw.text((phone_x + 30, content_y + 20), "Business Communication", fill="black", font=font)
    draw.text((phone_x + 30, content_y + 40), "Airport Dialogues", fill="black", font=font)
    
    # 添加播放按钮模拟
    for i in range(3):
        button_y = content_y + 60 + i * 30
        draw.ellipse([phone_x + 30, button_y, phone_x + 50, button_y + 20], 
                    fill="green", outline="darkgreen")
        draw.text((phone_x + 60, button_y + 2), f"Audio {i+1}", fill="black", font=font)
    
    # 添加飞机图标模拟 (简单的三角形)
    plane_x, plane_y = phone_x + 200, content_y + 80
    draw.polygon([(plane_x, plane_y), (plane_x + 30, plane_y + 10), (plane_x, plane_y + 20)], 
                fill="gray")
    
    # 保存图片
    output_dir = "output/images"
    os.makedirs(output_dir, exist_ok=True)
    img.save(f"{output_dir}/figure2_qr_code_interface.png", "PNG")
    print("Figure 2 created successfully!")

if __name__ == "__main__":
    create_figure2()

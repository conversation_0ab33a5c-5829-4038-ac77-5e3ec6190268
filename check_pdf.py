#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查PDF文件的完整性
"""

from pathlib import Path

def check_pdf(pdf_path):
    """检查PDF文件"""
    pdf_file = Path(pdf_path)
    
    if not pdf_file.exists():
        print(f"❌ PDF文件不存在: {pdf_path}")
        return False
    
    try:
        with open(pdf_file, 'rb') as f:
            content = f.read()
            
        print(f"📄 PDF文件大小: {len(content):,} 字节")
        print(f"📋 PDF头部: {content[:20]}")
        
        # 检查PDF格式
        if content.startswith(b'%PDF'):
            print("✅ PDF头部格式正确")
        else:
            print("❌ PDF头部格式错误")
            return False
            
        # 检查PDF结尾
        if b'%%EOF' in content[-100:]:
            print("✅ PDF结尾格式正确")
        else:
            print("❌ PDF结尾可能有问题")
            print(f"文件末尾: {content[-50:]}")
            
        # 检查是否包含图片
        if b'/Image' in content or b'/XObject' in content:
            print("✅ PDF包含图片对象")
        else:
            print("⚠️ PDF可能不包含图片")
            
        return True
        
    except Exception as e:
        print(f"❌ 读取PDF文件时出错: {e}")
        return False

if __name__ == "__main__":
    print("🔍 检查PDF文件完整性...")
    
    # 检查新生成的PDF
    print("\n📋 检查修复后的PDF:")
    check_pdf("test_figure_fix_output/output.pdf")
    
    # 检查原来的PDF作为对比
    print("\n📋 检查原来的PDF:")
    check_pdf("test_8565_double_column/output.pdf")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的图片处理功能
"""

import sys
from pathlib import Path
from docx_to_latex_correct import CorrectDocxToLatexConverter

def test_figure_fix():
    """测试修复后的图片处理"""
    print("🧪 测试修复后的图片处理功能...")
    
    # 假设有一个测试文档
    test_docx = Path("实例文档/双栏案列/FLS 8565 - 原始版本.docx")
    
    if not test_docx.exists():
        print(f"❌ 找不到测试文档: {test_docx}")
        return False
    
    # 创建输出目录
    output_dir = Path("test_figure_fix_output")
    
    try:
        # 转换文档
        converter = CorrectDocxToLatexConverter(str(test_docx), str(output_dir))
        converter.convert_to_latex()
        
        # 检查输出文件
        output_tex = output_dir / "output.tex"
        images_dir = output_dir / "images"
        
        if not output_tex.exists():
            print("❌ LaTeX文件未生成")
            return False
            
        if not images_dir.exists():
            print("❌ 图片目录未创建")
            return False
            
        # 检查图片文件
        image_files = list(images_dir.glob("figure*.png"))
        print(f"📁 找到 {len(image_files)} 个图片文件")
        
        # 检查LaTeX文件中的图片引用
        with open(output_tex, 'r', encoding='utf-8') as f:
            tex_content = f.read()
            
        # 统计图片引用
        import re
        figure_refs = re.findall(r'\\includegraphics.*?images/figure\d+\.png', tex_content)
        print(f"📄 LaTeX文件中有 {len(figure_refs)} 个图片引用")
        
        for ref in figure_refs:
            print(f"   - {ref}")
            
        # 检查是否有跨栏图片
        multicols_end = tex_content.count('\\end{multicols}')
        multicols_begin = tex_content.count('\\begin{multicols}{2}')
        
        print(f"🔄 跨栏控制: {multicols_end} 个结束, {multicols_begin} 个开始")
        
        if len(figure_refs) > 0:
            print("✅ 图片处理修复成功！")
            return True
        else:
            print("❌ 图片仍未正确插入")
            return False
            
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return False

if __name__ == "__main__":
    success = test_figure_fix()
    sys.exit(0 if success else 1)

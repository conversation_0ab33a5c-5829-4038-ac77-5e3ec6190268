\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand*\HyPL@Entry[1]{}
\bibstyle{plainnat}
\bibstyle{plain}
\citation{1}
\citation{2}
\citation{3,4,5}
\HyPL@Entry{0<</S/D>>}
\@writefile{toc}{\contentsline {section}{\numberline {1}Introduction}{1}{section.1}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {2}Materials and Methods}{1}{section.2}\protected@file@percent }
\citation{6,7,8}
\@writefile{toc}{\contentsline {section}{\numberline {3}Results}{2}{section.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.1}Subsection}{2}{subsection.3.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {3.1.1}Subsubsection}{2}{subsubsection.3.1.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.2}Figures, Tables and Schemes}{2}{subsection.3.2}\protected@file@percent }
\providecommand*\caption@xref[2]{\@setref\relax\@undefined{#1}}
\newlabel{Figure 1.}{{\caption@xref {Figure 1.}{ on input line 432}}{2}{Figures, Tables and Schemes}{figure.caption.1}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces This is a figure. Schemes follow the same formatting.}}{2}{figure.caption.1}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {1}{\ignorespaces This is a table. Tables should be placed in the main text near to the first time they are cited.}}{2}{table.caption.2}\protected@file@percent }
\newlabel{tab1}{{1}{2}{This is a table. Tables should be placed in the main text near to the first time they are cited}{table.caption.2}{}}
\newlabel{Figure 2.}{{\caption@xref {Figure 2.}{ on input line 466}}{3}{Figures, Tables and Schemes}{figure.caption.3}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces This is a figure. Schemes follow another format. If there are multiple panels, they should be listed as: (\textbf  {a}) Description of what is contained in the first panel; (\textbf  {b}) Description of what is contained in the second panel. \textbf  {Figures} should be placed in the main text near to the first time they are cited.}}{3}{figure.caption.3}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {2}{\ignorespaces This is a table. Tables should be placed in the main text near to the first time they are cited.}}{3}{table.caption.4}\protected@file@percent }
\newlabel{tab2}{{2}{3}{This is a table. Tables should be placed in the main text near to the first time they are cited}{table.caption.4}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {3.3}Formatting of Mathematical Components}{3}{subsection.3.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {4}Discussion}{4}{section.4}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {5}Conclusions}{4}{section.5}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {6}Patents}{4}{section.6}\protected@file@percent }
\bibcite{1}{{1}{}{{}}{{}}}
\bibcite{2}{{2}{}{{}}{{}}}
\bibcite{3}{{3}{}{{}}{{}}}
\bibcite{4}{{4}{}{{}}{{}}}
\bibcite{5}{{5}{}{{}}{{}}}
\bibcite{6}{{6}{}{{}}{{}}}
\bibcite{7}{{7}{}{{}}{{}}}
\bibcite{8}{{8}{}{{}}{{}}}
\providecommand\NAT@force@numbers{}\NAT@force@numbers
\ttl@finishall
\gdef \@abspage@last{6}

\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\bibstyle{plain}
\citation{1}
\citation{2}
\citation{3,4,5}
\citation{6,7,8}
\@writefile{toc}{\contentsline {section}{\numberline {1}Introduction}{1072}{section.1}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {2}Materials and Methods}{1072}{section.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {3}Results}{1072}{section.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.1}Subsection}{1072}{subsection.3.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {3.1.1}Subsubsection}{1072}{subsubsection.3.1.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.2}Figures, Tables and Schemes}{1072}{subsection.3.2}\protected@file@percent }
\providecommand*\caption@xref[2]{\@setref\relax\@undefined{#1}}
\newlabel{Figure 1.}{{\caption@xref {Figure 1.}{ on input line 410}}{1072}{Figures, Tables and Schemes}{figure.caption.2}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces This is a figure. Schemes follow the same formatting.}}{1072}{figure.caption.2}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {1}{\ignorespaces This is a table. Tables should be placed in the main text near to the first time they are cited.}}{1072}{table.caption.3}\protected@file@percent }
\newlabel{tab1}{{1}{1072}{This is a table. Tables should be placed in the main text near to the first time they are cited}{table.caption.3}{}}
\newlabel{Figure 2.}{{\caption@xref {Figure 2.}{ on input line 445}}{1073}{}{figure.caption.4}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces This is a figure. Schemes follow another format. If there are multiple panels, they should be listed as: (\textbf  {a}) Description of what is contained in the first panel; (\textbf  {b}) Description of what is contained in the second panel. Figures should be placed in the main text near to the first time they are cited.}}{1073}{figure.caption.4}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {2}{\ignorespaces This is a table. Tables should be placed in the main text near to the first time they are cited.}}{1073}{table.caption.5}\protected@file@percent }
\newlabel{tab2}{{2}{1073}{This is a table. Tables should be placed in the main text near to the first time they are cited}{table.caption.5}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {3.3}Formatting of Mathematical Components}{1073}{subsection.3.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {4}Discussion}{1074}{section.4}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {5}Conclusions}{1074}{section.5}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {6}Patents}{1074}{section.6}\protected@file@percent }
\citation{1}
\bibcite{1}{{1}{}{{}}{{}}}
\bibcite{2}{{2}{}{{}}{{}}}
\bibcite{3}{{3}{}{{}}{{}}}
\bibcite{4}{{4}{}{{}}{{}}}
\bibcite{5}{{5}{}{{}}{{}}}
\bibcite{6}{{6}{}{{}}{{}}}
\bibcite{7}{{7}{}{{}}{{}}}
\bibcite{8}{{8}{}{{}}{{}}}
\bibcite{9}{{9}{}{{}}{{}}}
\providecommand\NAT@force@numbers{}\NAT@force@numbers
\ttl@finishall
\gdef \@abspage@last{5}

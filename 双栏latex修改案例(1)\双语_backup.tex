\documentclass[11pt]{article} % 指定文档类型 [可以指定字号，纸张大小]{文档类型包括article,book,beamer(英文文档),ctexart,ctexbook,ctexbeamer(中文文档)等}
% 导言区: 全局设置, 宏包调用等。（以下部分是对全局有效果的区域）
%\usepackage{showframe}  % 显示页面边框
\usepackage{flushend} 
\usepackage{graphicx}
\usepackage{balance} 
\usepackage{balance}
\usepackage[paperwidth=210mm, paperheight=285mm, top=2.5cm, bottom=2.5cm, left=2cm, right=2cm]{geometry}%页面设置纸张类型和边距
\usepackage{fancyhdr}%页眉页脚的宏包
\usepackage[absolute, overlay]{textpos}
\usepackage{graphicx}
\usepackage{subcaption}
%\usepackage{luatexja-fontspec}
%\setmainjfont{FandolSong}

\usepackage{amsmath} % AMS 数学公式 宏包 
\usepackage{amssymb} % AMS 数学符号 宏包 
\usepackage{amsfonts} % AMS 数学字体 宏包
\usepackage{amsthm, bm} % 数学
\usepackage{changepage}
\usepackage[mathlines]{lineno}
\usepackage{xcolor}%颜色宏包
\usepackage{tcolorbox} % 导入 tcolorbox 宏包，用于创建带颜色背景的框
\usepackage{multicol} % 导入multicol宏包分栏！！
% \usepackage{microtype}
\usepackage{etoolbox}%参考文献自定义宏包
\usepackage{enumitem}
\usepackage{ragged2e} % 导入 ragged2e 宏包用于设置对齐
%字体相关宏包
\usepackage{titlesec}%标题自定义宏包
\usepackage{fontspec}
\usepackage{parskip}
\usepackage{titling}
\usepackage{lipsum} % 用于生成示例文本
\usepackage{setspace}%行间距相关宏包
%链接到文献地址宏包
%修改参考文献的数字的格式的
\usepackage[numbers,sort\&compress]{natbib}
%第一段首行缩进
\usepackage{indentfirst}
\usepackage{microtype}
\usepackage{titletoc}

%++++++list 宏包
\setitemize{topsep=3pt,parsep=0pt,itemsep=0pt,leftmargin=*,labelsep=5.5mm,align=parleft}
\setenumerate{topsep=3pt,parsep=0pt,itemsep=0pt,leftmargin=*,labelsep=5.5mm,align=parleft}
\setlist[description]{itemsep=0mm}

%表格宏包
\usepackage{placeins}

\usepackage{makecell}
\usepackage{caption}
\captionsetup{labelsep=period}
\usepackage{booktabs}  % 用于创建漂亮的表格
\usepackage[colorlinks,linkcolor=black,urlcolor=black,citecolor=black]{hyperref}
\urlstyle{same}  %++++++为了网址颜色相同

\usepackage{url} %++++++为了网址断行
\def\UrlBreaks{\do\A\do\B\do\C\do\D\do\E\do\F\do\G\do\H\do\I\do\J
\do\K\do\L\do\M\do\N\do\O\do\P\do\Q\do\R\do\S\do\T\do\U\do\V
\do\W\do\X\do\Y\do\Z\do\[\do\\\do\]\do\^\do\_\do\`\do\a\do\b
\do\c\do\d\do\e\do\f\do\g\do\h\do\i\do\j\do\k\do\l\do\m\do\n
\do\o\do\p\do\q\do\r\do\s\do\t\do\u\do\v\do\w\do\x\do\y\do\z
\do\.\do\@\do\\\do\/\do\!\do\_\do\|\do\;\do\>\do\]\do\)\do\,
\do\?\do\'\do+\do\=\do\#}%++++++为了网址断行

\usepackage{tabularx}%表格通栏显示的宏
%\usepackage{multirow} % 注释掉，避免包缺失问题
\usepackage{float} % 引入 float 宏包，以便使用 [H] 参数
\renewcommand{\arraystretch}{1}%表格的行距
\newcolumntype{C}{>{\centering\arraybackslash}X} % 定义新的列类型 C

\setlength{\abovecaptionskip}{3pt} % 设置标题与表格上方的距离
\setlength{\belowcaptionskip}{0pt}  % 设置标题与表格下方的距离
\usepackage[justification=centering]{caption}
\captionsetup{
    font={footnotesize}, % 设置标题字体为小号字体 (9pt)
    justification=centering, % 设置标题居中对齐
    labelfont=bf, % 设置标签字体为粗体
    textfont=normalfont  % 设置标题文字为粗体
}
%\usepackage{capt-of}  % 注释掉，避免包缺失问题
%环境

\renewenvironment{proof}[1][\proofname]{\par %% \proofname allows to have "Proof of my theorem"
  \pushQED{\qed}%
  \normalfont \topsep6\p@\@plus6\p@\relax
  \trivlist
  \item[\hskip\labelsep
        \bfseries %% "Proof" is bold
    #1\@addpunct{.}]\ignorespaces %% Period instead of colon
}{%
  \popQED\endtrivlist\@endpefalse
}

\newcounter{theorem}
 \setcounter{theorem}{0}
 \newtheorem{Theorem}[theorem]{Theorem}
 
 \newcounter{lemma}
 \setcounter{lemma}{0}
 \newtheorem{Lemma}[lemma]{Lemma}
 
 \newcounter{corollary}
 \setcounter{corollary}{0}
 \newtheorem{Corollary}[corollary]{Corollary}
 
 \newcounter{proposition}
 \setcounter{proposition}{0}
 \newtheorem{Proposition}[proposition]{Proposition}
 
 \newcounter{characterization}
 \setcounter{characterization}{0}
 \newtheorem{Characterization}[characterization]{Characterization}
 
 \newcounter{property}
 \setcounter{property}{0}
 \newtheorem{Property}[property]{Property}
 
 \newcounter{problem}
 \setcounter{problem}{0}
 \newtheorem{Problem}[problem]{Problem}
 
 \newcounter{example}
 \setcounter{example}{0}
 \newtheorem{Example}[example]{Example}
 
 \newcounter{examplesanddefinitions}
 \setcounter{examplesanddefinitions}{0}
 \newtheorem{ExamplesandDefinitions}[examplesanddefinitions]{Examples and Definitions}
 
 \newcounter{remark}
 \setcounter{remark}{0}
 \newtheorem{Remark}[remark]{Remark}
 
 \newcounter{definition}
 \setcounter{definition}{0}
 \newtheorem{Definition}[definition]{Definition}
 
 \newcounter{hypothesis}
 \setcounter{hypothesis}{0}
 \newtheorem{Hypothesis}[hypothesis]{Hypothesis}

 \newcounter{notation}
 \setcounter{notation}{0}
 \newtheorem{Notation}[notation]{Notation}
 
 \newcounter{assumption}
 \setcounter{assumption}{0}
 \newtheorem{Assumption}[assumption]{Assumption}
 
 \newcounter{algorithm}
 \setcounter{algorithm}{0}
 \newtheorem{Algorithm}[algorithm]{Algorithm}

  % Define left/right mark in math environment
\let\originalleft\left
\let\originalright\right
\renewcommand{\left}{\mathopen{}\mathclose\bgroup\originalleft}
\renewcommand{\right}{\aftergroup\egroup\originalright}

%定义proof样式
\makeatletter
\renewenvironment{proof}[1][\proofname]{\par
  \pushQED{\qed}%
  \normalfont\topsep6\p@\@plus6\p@\relax
  \trivlist
  \item[\hskip\labelsep
        \bfseries #1\@addpunct{.}]\ignorespaces
}{%
  \popQED\endtrivlist\@endpefalse
}
 
% 自定义参考文献字号10pt
\apptocmd{\thebibliography}{\small}{}{}
%这是设置注释的角标格式的
\setcitestyle{super,open=[,close=],citesep={, }}
\bibliographystyle{plain}
%2、定义全文字体为Times New Roman
\setmainfont{Times New Roman}
%4 设置首行缩进为 2 字符
\setlength{\parindent}{2em}
\setlength{\parskip}{0pt} % 设置段落之间的距离为0

%页眉设置开始------
% 首页的自定义页眉样式
% \pagestyle{fancy}
\fancypagestyle{first}{%
  % 清除默认的页眉和页脚设置
  \fancyhf{}
  \renewcommand{\headrulewidth}{0.75pt} % 移除页眉的线
  \renewcommand{\headrule}{\color{black}\hrule width\headwidth height \headrulewidth} % 设置页眉线颜色
  % \setlength{\headheight}{25pt} % 设置页眉高度
  % 设置页眉内容
  \fancyhead[C]{
    \par\vspace{0.6pt}%段前
    {\noindent\fontsize{9pt}{10pt}\selectfont{ \textit{\textbf{Forum for Linguistic Studies}} |  Volume 07 | Issue 07 | July 2025}}%
    \par\vspace{-12pt}%段后
  }
  
  \setlength{\headsep}{4mm} % 设置页眉线与正文之间的距离
  % 保留页脚的位置

  \fancyfoot[C]{\thepage}
  \renewcommand{\footrulewidth}{0pt} % 页脚的线
}
%页眉公共样式
\fancypagestyle{other}{%
    \fancyhf{}
    \renewcommand{\headrulewidth}{0.75pt} % 页眉的线
    \renewcommand{\headrule}{\color{black}\hrule width\headwidth height \headrulewidth} % 设置页眉线颜色
   % \setlength{\headheight}{18pt} % 设置页眉高度
    % 设置页眉内容
    \fancyhead[C]{\par\vspace{0.6pt}%段前
    {\noindent\fontsize{9pt}{10pt}\selectfont{ \textit{\textbf{Forum for Linguistic Studies}} | Volume 07 | Issue 07 | July 2025}} \par\vspace{-12pt}%段后
    }
     \setlength{\headsep}{7mm} % 设置页眉线与正文之间的距离
    % 设置页脚内容
    \setcounter{page}{1071}%序号从特定值开始的时候加
    \fancyfoot[C]{\thepage}
    \renewcommand{\footrulewidth}{0pt} % 页脚的线
}

%页眉设置结束---------
%文章类型
\newcommand{\articletype}[1]{%
    \par\vspace{9.55pt}%段前
    {\noindent\fontsize{12pt}{19.2pt}\bf\selectfont #1}%
    \par\vspace{10pt}%段后
}
%文章标题
\newcommand{\articletitle}[1]{%
    \par\vspace{0.2pt}%段前
    \noindent\parbox{\textwidth}{\raggedright\fontsize{16pt}{19.2pt}\bfseries #1}% 居左对齐
    \par\vspace{0pt}%段后
}
%文章标题
\newcommand{\articleabstract}[1]{%
    \par\vspace{10pt}%段前
    \noindent\parbox{\textwidth}{\centering\fontsize{12pt}{14.4pt}\bfseries #1}% 居中对齐
    \par\vspace{5pt}%段后
}
%自定义颜色
\definecolor{black}{HTML}{000000} % 定义颜色
\definecolor{bgcolor}{HTML}{d8d8d8} % 定义颜色
\definecolor{blue}{HTML}{1F497D} % 定义颜色
\definecolor{red}{HTML}{FF0000} % 定义颜色
% 自定义链接
\newcommand{\ORCID}{https://www.baidu.com}
%模板应用自定义样式
\newcommand{\supernum}[1]{\textsuperscript{\bfseries{#1}}}

%自定标题样式
 \par\vspace{5pt}%段前
\renewcommand\refname{References}
 \par\vspace{0pt} % 设置距离为10磅
%以下是常用格式
% 定义第一级标题的格式
\titleformat{\section}
  {\fontsize{15pt}{18}\bfseries\selectfont} % 设置字号为15pt，加粗
  {\thesection.} % 标题编号格式
  {0.5em} % 标题编号和标题文字的距离
  {} % 可以在这里添加额外的格式设定，比如颜色等
% 设置 section 标题的段前和段后间距
\titlespacing*{\section}{0pt}{15pt}{10pt} % 标题文字与正文间距


% 定义第二级标题的格式
\titleformat{\subsection}
  {\color{black}\bfseries\fontsize{12}{14.4}\selectfont}
  {\thesubsection.}
  {0.5em}
  {}
\titlespacing*{\subsection}{0pt}{15pt}{10pt}

% 定义第三级标题的格式
\titleformat{\subsubsection}
  {\color{black}\bfseries\fontsize{12}{14.4}\selectfont}
  {\thesubsubsection.}
  {0.5em}
  {}
\titlespacing*{\subsubsection}{0pt}{5pt}{5pt}

\newcommand{\subsubsubsection}[1]{%
    \par\vspace{5pt}%
    \noindent{\color{black}\fontsize{12}{14.4}\selectfont #1}%
    \par\vspace{5pt}%
}
% 重新定义公式编号格式
\renewcommand{\theequation}{\arabic{equation}}
\begin{document}%{这个括号中是环境名称}

\begin{figure}[b]
  \begin{tcolorbox}[colback=bgcolor, colframe=gray!0!white, boxrule=0mm, arc=0mm, left=2mm, right=2mm, top=2mm, bottom=2mm]
    % \par\vspace{5pt}%
   \fontsize{8}{10}\selectfont{\color{blue}\MakeUppercase{*Corresponding Author:}}
    \par\vspace{1.64pt}%
    \fontsize{8}{10}\selectfont{corresponding author name, corresponding author affiliation; Email: <EMAIL>}\par\vspace{10pt}%
    \fontsize{8}{10}\selectfont{\color{blue}\MakeUppercase{ARTICLE INFO}}
    \par\vspace{1.64pt}%
    \fontsize{8}{10}{Received: Day Month Year | Revised: Day Month Year | Accepted: Day Month Year | Published Online: Day Month Year\\
    DOI: https://doi.org/10.30564/xxxx.vxix.xxxx}\par\vspace{10pt}%
    \fontsize{8}{10}\selectfont{\color{blue}\MakeUppercase{CITATION}}
    \par\vspace{1.64pt}%
    \fontsize{8}{10}{Lastname, The initials of the firstname., Lastname, The initials of the firstname., Lastname, The initials of the firstname., et al., 2024. Title. 
Journal Name. x(x): x–x. DOI: https://doi.org/10.30564/xxxx.vxix.xxxx}\par\vspace{10pt}%
    \fontsize{8}{10}\selectfont{\color{blue}\MakeUppercase{Copyright}}
    \par\vspace{1.64pt}%
    \fontsize{8}{10}{Copyright © 2024 by the author(s). Published by Bilingual Publishing Group. This is an open access article under the Creative Commons Attribution-NonCommercial 4.0 International (CC BY-NC 4.0) License (https://creativecommons.org/licenses/by-nc/4.0/).}%
  \end{tcolorbox}
\end{figure}
\vspace{12pt}
\noindent\begin{minipage}[c]{\dimexpr3.7cm+20pt} % 左边部分占据三分之一的宽度
 % \vfill % 垂直填充
 % \hspace{0pt} % 设置左侧间距为10pt
  \includegraphics[width=3.7cm]{logo.png} % 插入图片
 % \hspace{0pt} % 设置右侧间距为10pt
%  \vfill % 垂直填充
\end{minipage}
\hfill % 用于分隔左右部分
\hspace{-22pt}
\noindent\begin{minipage}[c]{\dimexpr\textwidth-3.7cm\relax} % 右边部分占据剩余的宽度
 \vspace{2.7mm}
\begin{tcolorbox}[colback=bgcolor, colframe=gray!0!white, boxrule=0mm, arc=0mm, left=0mm, right=0mm, top=0mm, bottom=0mm, height=18mm] % 去掉所有边距
\centering{
     \fontsize{12pt}{14pt}\selectfont{\vspace{8.55pt}\textbf{Journal Name}\vspace{7pt}\\
      https://journals.bilpubgroup.com/xxxxxx\vspace{10pt}}
    }
  \end{tcolorbox}
  \vspace{-0.8mm}
\end{minipage}
{\noindent\color{black}\rule{\linewidth}{1pt}} 
\articletype{ARTICLE}
\vspace{6pt}
\articletitle{Innovation for Developing English Communication Skills of Personnel at Buriram Airport}
%作者
\vspace{3pt} % 段前3磅
\begin{center}%
 {\fontsize{10pt}{12pt}\selectfont \textbf{\textit{Firstname Lastname \supernum{1}\href{https://orcid.org/0000-0003-3203-8743}{\includegraphics[width=0.46cm]{ORCID.png}}, Firstname Lastname \supernum{2*}\href{https://orcid.org/0000-0002-0145-5766}{\includegraphics[width=0.46cm]{ORCID.png}}}}}
\end{center}
\vspace{5pt} % 段后5磅
%作者介绍
\noindent\fontsize{10pt}{15pt}\selectfont\textit{\supernum{1} University Department, University Name, City State ZIP/Zone, Country}\\
\noindent\fontsize{10pt}{15pt}\selectfont\textit{\supernum{2} Group, Laboratory, City State ZIP/Zone, Country}\\
{\noindent\color{black}\rule{\linewidth}{0.5pt}}
\articleabstract{ ABSTRACT }
 \fontsize{10}{15}\selectfont{A general introduction to the research topic of the paper should be provided, along with a brief summary of its main results and implications. Kindly ensure the abstract is self-contained and remains readable to a wider audience. The abstract should be an objective representation of the article and it must not contain results that are not presented and substantiated in the main text and should not exaggerate the main conclusions. The abstract should also be kept to a maximum of 200–250 words.A general introduction to the research topic of the paper should be provided, along with a brief summary of its main results and implications. Kindly ensure the abstract is self-contained and remains readable to a wider audience. The abstract should be an objective representation of the article and it must not contain results that are not presented and substantiated in the main text and should not exaggerate the main conclusions. The abstract should also be kept to a maximum of 200–250 words.A general introduction to the research topic of the paper should be provided, along with a brief summary of its main results and implications. Kindly ensure the abstract is self-contained and remains readable to a wider audience. The abstract should be an objective representation of the article and it must not contain results that are not presented and substantiated in the main text and should not exaggerate the main conclusions. The abstract should also be kept to a maximum of 200–250 words.}\\ 
\thispagestyle{first}
\pagestyle{other}
{\noindent\fontsize{10}{15}\selectfont{\textit{\textbf{Keywords: }}  Keyword 1; Keyword 2; Keyword 3}}
\vspace{9mm}
 %{\noindent\color{black}\rule{\linewidth}{2.25pt}} 
\begin{multicols}{2}
  \section{Introduction}

  The introduction should briefly place the study in a broad context and highlight why it is important, in particular, in relation to the current state of research in the field. Finally, it can conclude with a brief statement of the aim of the work and a comment about whether that aim was achieved \cite{1}.

\section{Materials and Methods}

The Materials and Methods should be described with sufficient details to allow others to replicate and build on the published results. Please note that the publication of your manuscript implicates that you must make all materials, data, computer code, and protocols associated with the publication available to readers \cite{2}. Please disclose at the submission stage any restrictions on the availability of materials or information. New methods and protocols should be described in detail while well-established methods can be briefly described and appropriately cited.


Research manuscripts reporting large datasets that are deposited in a publicly available database should specify where the data have been deposited and provide the relevant accession numbers \cite{3,4,5}. If the accession numbers have not yet been obtained at the time of submission, please state that they will be provided during review. They must be provided prior to publication.


Interventionary studies involving animals or humans, and other studies that require ethical approval, must list the authority that provided approval and the corresponding ethical approval code.


\section{Results}

Provide a concise and precise description of the experimental results, their interpretation as well as the experimental conclusions that can be drawn. 

\subsection{Subsection}

\subsubsection{Subsubsection}

Bulleted lists look like this:

\vspace{3pt}
\begin{itemize}%++++++这块删了 [leftmargin=11mm]， 前言加了相关宏包
\item	 First bullet;
\item	 Second bullet;
\item	 Third bullet.
\end{itemize}
\vspace{3pt}

Numbered lists can be added as follows:

\vspace{3pt}
\begin{enumerate}%++++++这块删了 [leftmargin=12mm]， 前言加了相关宏包 
\item	First item; 
\item	Second item;
\item	Third item.
\end{enumerate}
\vspace{3pt}

The text continues here.

\subsection{Figures, Tables and Schemes}

Figures include photographs, scanned images, graphs, charts and schematic diagrams. These captions should be numbered (e.g., \textbf{Figure 1}, \textbf{Figure 2}, etc.). All figures and tables must have a brief title (also known as caption) that describes the entire figure without citing specific panels, followed by a legend, defined as a description of each panel. Please identify each panel with uppercase letters in parenthesis (e.g., (A), (B), (C), etc.) All figures should be legible in print form and of optimal resolution \cite{6,7,8}.


All figures and tables should be cited in the main text as \textbf{Figure 1}, \textbf{Table 1}, etc.


\begin{figure}[H]
\centering
\label{Figure 1.}
\includegraphics[width=0.25\linewidth]{image1.png}
\vspace{6pt}
\captionsetup{labelfont=bf, labelsep=period, justification=raggedright}
\caption{This is a figure. Schemes follow the same formatting.}
\end{figure}


\vspace{-8pt}
\begin{table}[H]
\captionsetup{justification=raggedright,singlelinecheck=false}
\caption{This is a table. Tables should be placed in the main text near to the first time they are cited.\label{tab1}}
\vspace{3pt}
\small
\tabcolsep=0.88cm
\begin{tabular}{ccc}
\toprule[0.5pt]
\textbf{Title 1} & \textbf{Title 2} & \textbf{Title 3} \\
\midrule[0.25pt]
entry 1 & data & data \\
entry 2 & data & data $^{1}$ \\
\bottomrule[0.5pt]
\end{tabular}
 \vspace{1mm}%++++++这块修改了脚注格式
 \parbox{\textwidth}{\fontsize{7pt}{8}\selectfont{\textsuperscript{1} Tables may have a footer.}}%++++++这块修改了脚注格式
\end{table}


The text continues here (\textbf{Figure 2} and \textbf{Table 2}).

\end{multicols}

\vspace{-6pt}
\begin{figure}[H]
\centering
\label{Figure 2.}
\begin{tabular}{cc}
\includegraphics[width=0.25\linewidth]{image2a.png}&\includegraphics[width=0.25\linewidth]{image2b.png}\\
(\textbf{a})&(\textbf{b})
\end{tabular}
\vspace{6pt}

\captionsetup{justification=justified,singlelinecheck=false}
\caption{This is a figure. Schemes follow another format. If there are multiple panels, they should be listed as: (\textbf{a}) Description of what is contained in the first panel; (\textbf{b}) Description of what is contained in the second panel. Figures should be placed in the main text near to the first time they are cited.}
\end{figure}


\vspace{-8pt}
\begin{table}[H]
%\captionsetup{justification=raggedright,singlelinecheck=false}
\caption{This is a table. Tables should be placed in the main text near to the first time they are cited.\label{tab2}}
\small
\newcolumntype{C}{>{\centering\arraybackslash}X}
\begin{tabularx}{\textwidth}{CCCC}
\toprule[0.5pt]

\multicolumn{2}{c}{\textbf{Title 1}} & \multicolumn{2}{c}{\textbf{Title 2}}\\%++++++加了跨列命令
\midrule[0.25pt]
\textbf{Title 3}& \textbf{Title 4}& \textbf{Title 5}& \textbf{Title 6}\\
\midrule[0.25pt]
\multirow[m]{3}{*}{Entry 1 *}	& Data	& Data		& Data\\
    & Data		& Data	& Data\\
  & Data		& Data	& Data\\
 \midrule[0.25pt]
\multirow[m]{3}{*}{Entry 2}    & Data		& Data	& Data\\
	 & Data		& Data	& Data\\
	 & Data	& Data	& Data\\
\midrule[0.25pt]
\multirow[m]{3}{*}{Entry 3}    & Data	 & Data	& Data\\
     & Data	& Data	& Data\\
 & Data	& Data	& Data\\
 \midrule[0.25pt]
\multirow[m]{3}{*}{Entry 4}   & Data		& Data	& Data\\
     & Data & Data	& Data\\
	& Data	& Data	& Data\\
\bottomrule[0.5pt]
\end{tabularx}
 \vspace{1mm}%++++++这块修改了脚注格式
 \parbox{\textwidth}{\fontsize{7pt}{8}\selectfont{\textsuperscript{1} Tables may have a footer.}}%++++++这块修改了脚注格式
\end{table}

\begin{multicols}{2}
\subsection{Formatting of Mathematical Components}

This is the example 1 of equation:
\begin{equation}
\rm a = 1,
\end{equation}
the text following an equation need not be a new paragraph. Please punctuate equations as regular text.


This is the example 2 of equation:
\begin{equation}
\begin{split}
\rm a = & \rm  ~b + c + d + e + f + g + h + i + j + k + l\\
&\rm + m + n + o + p + q + r + s + t + u \\
&\rm + v + w + x + y + z
\end{split}
\end{equation}
the text following an equation need not be a new paragraph. Please punctuate equations as regular text.

Theorem-type environments (including propositions, lemmas, corollaries etc.) can be formatted as follows:

\vspace{6pt}
%% Example of a theorem:
\begin{Theorem}
Example text of a theorem. Theorems, propositions, lemmas, etc. should be numbered sequentially (i.e., Proposition 2 follows Theorem 1). Examples or Remarks use the same formatting, but should be numbered separately, so a document may contain Theorem 1, Remark 1 and Example~1.
\end{Theorem}
\vspace{6pt}


The text continues here. Proofs must be formatted as follows:

\vspace{6pt}
%% Example of a proof:
\begin{proof}[Proof of Theorem 1]
Text of the proof. Note that the phrase “of Theorem 1” is optional if it is clear which theorem is being referred to. Always finish a proof with the following symbol.
\end{proof}
\vspace{6pt}

\newcolumn
The text continues here.

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Discussion}

Authors should discuss the results and how they can be interpreted from the perspective of previous studies and of the working hypotheses. The findings and their implications should be discussed in the broadest context possible. Future research directions may also be highlighted.

\section{Conclusions}

This should clearly explain the main conclusions of the article, highlighting its importance and relevance. This section is not mandatory but can be added to the manuscript if the discussion is unusually long or complex.

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Patents}

This section is not mandatory, but may be added if there are patents resulting from the work reported in this manuscript.

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\vspace{6pt} 

\section*{Supplementary Materials}

Supplementary Material should be uploaded separately on submission.

\vspace{15pt}
\noindent
{\fontsize{15pt}{18pt}\selectfont\textbf{Author Contributions}}
\vspace{10pt}


For research articles with several authors, a short paragraph specifying their individual contributions must be provided. The following statements should be used “Conceptualization, X.X. and Y.Y.; methodology, X.X.; software, X.X.; validation, X.X., Y.Y. and Z.Z.; formal analysis, X.X.; investigation, X.X.; resources, X.X.; data curation, X.X.; writing—original draft preparation, X.X.; writing—review and editing, X.X.; visualization, X.X.; supervision, X.X.; project administration, X.X.; funding acquisition, Y.Y. All authors have read and agreed to the published version of the manuscript.” Authorship must be limited to those who have contributed substantially to the work reported.


\section*{Funding}

All sources of funding for the study should be disclosed. Clearly indicate grants that you have received in support of your research work and if you received funds to cover publication costs. Please add: “This work received no external funding” or This work was supported by [name of funder] grant number [xxx].

\section*{Institutional Review Board Statement}

In this section, you should add the Institutional Review Board Statement and approval number, if relevant to your study. You might choose to exclude this statement if the study did not require ethical approval. Please note that the Editorial Office might ask you for further information. Please add “The study was conducted in accordance with the Declaration of Helsinki, and approved by the Institutional Review Board (or Ethics Committee) of NAME OF INSTITUTE (protocol code XXX and date of approval).” for studies involving humans. OR “The animal study protocol was approved by the Institutional Review Board (or Ethics Committee) of NAME OF INSTITUTE (protocol code XXX and date of approval).” for studies involving animals. OR “Ethical review and approval were waived for this study due to REASON (please provide a detailed justification).” OR “Not applicable” for studies not involving humans or animals.

\section*{Informed Consent Statement}

Any research article describing a study involving humans should contain this statement. Please add “Informed consent was obtained from all subjects involved in the study.” OR “Patient consent was waived due to REASON (please provide a detailed justification).” OR “Not applicable.” for studies not involving humans. You might also choose to exclude this statement if the study did not involve humans.


Written informed consent for publication must be obtained from participating patients who can be identified (including by the patients themselves). Please state “Written informed consent has been obtained from the patient(s) to publish this paper” if applicable.

\section*{Data Availability Statement}

We encourage all authors of articles published in our journals to share their research data. In this section, please provide details regarding where data supporting reported results can be found, including links to publicly archived datasets analyzed or generated during the study. Where no new data were created, or where data is unavailable due to privacy or ethical restrictions, a statement is still required.


\section*{Acknowledgments}

In this section, you can acknowledge any support given which is not covered by the author contribution or funding sections. This may include administrative and technical support, or donations in kind (e.g., materials used for experiments).




\section*{Confict of Interest}

Declare conflicts of interest or state “The authors declare no conflict of interest.” Authors must identify and declare any personal circumstances or interest that may be perceived as inappropriately influencing the representation or interpretation of reported research results. Any role of the funders in the design of the study; in the collection, analyses or interpretation of data; in the writing of the manuscript; or in the decision to publish the results must be declared in this section. If there is no role, please state “The funders had no role in the design of the study; in the collection, analyses, or interpretation of data; in the writing of the manuscript; or in the decision to publish the results”.

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% Optional



\section*{Appendix  A}
The appendix is an optional section that can contain details and data supplemental to the main text---for example, explanations of experimental details that would disrupt the flow of the main text but nonetheless remain crucial to understanding and reproducing the research shown; figures of replicates for experiments of which representative data are shown in the main text can be added here if brief, or as Supplementary Data. Mathematical proofs of results not central to the paper can be added as an appendix.


\section*{Appendix  B}
All appendix sections must be cited in the main text. In the appendices, Figures, Tables, etc. should be labeled, starting with ``A''---e.g., \textbf{Figure A1}, \textbf{Figure A2}, etc\cite{1}.

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

 \begin{thebibliography}{99}
    \singlespacing           %++++++单倍行距
    \setlength{\itemsep}{0pt}  %++++++这块修改了脚注格式 去除条目之间的垂直间距
    % \setlength{\parsep}{0pt}  %++++++这块修改了脚注格式去除段落之间的垂直间距
    %\vspace{-\baselineskip}
    \vspace{-12pt}
    \providecommand{\doi}[1]{doi: #1}

\bibitem{1} Author 1, A.B., Author 2, C.D., Year. Title of The Article. Journal Name. Volume(issue number), page range. DOI: https://doi.org/

\bibitem{2} Author 1, A.; Author 2, B., Year. Book Title, 3rd ed. Publisher: Publisher Location, Country. pp. page range.

\bibitem{3} Author 1, A., Author 2, B., Year. Title of the Chapter. In: Editor 1, A., Editor 2, B. (eds.). Book Title, 2nd ed. Publisher: Publisher Location, Country. pp. page range.

\bibitem{4} Author 1, A.B., Author 2, C., Author 3, M., et al., Year. Title of the Chapter. Proceedings of The Name of the Conference; Date of Conference (Month Day–Month Day, Year); Location of Conference (City, State Abbreviation). pp. page range.

\bibitem{5} Author 1, A.B., Author 2, C., Author 3, M., et al., Year. Website title. Available from: website (cited Day Month Year).

\bibitem{6} Author 1, A.B., Year. Title of Thesis [Level of Thesis]. City, State Abbreviation: University name. pp. page range.

\bibitem{7} Standards number. Year. Title of Standards.

\bibitem{8} Unit/Institution, Year. Report title. Report number, Day Month Year.

\bibitem{9} Author 1, A.B., Author 2, C. (inventors), Year. Patent name. Country name Patent. Patent number. Year Month Day.

\end{thebibliography}

\end{multicols} % 如果需要多栏，取消注释

\end{document}


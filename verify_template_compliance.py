#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面验证转换程序是否严格按照LaTeX模板设置
"""

import re
from pathlib import Path

def verify_template_compliance():
    """验证转换程序是否严格按照模板设置"""
    print("🔍 全面验证转换程序是否严格按照LaTeX模板设置...")

    # 原始模板文件
    template_file = Path("双栏latex修改案例(1)/双语.tex")
    converter_file = Path("docx_to_latex_correct.py")

    if not template_file.exists():
        print(f"❌ 找不到模板文件: {template_file}")
        return False

    if not converter_file.exists():
        print(f"❌ 找不到转换程序: {converter_file}")
        return False

    # 读取文件内容
    with open(template_file, 'r', encoding='utf-8') as f:
        template_content = f.read()

    with open(converter_file, 'r', encoding='utf-8') as f:
        converter_content = f.read()

    print("\n📋 全面设置对比检查:")

    # 扩展的检查项目列表
    checks = [
        # 页面和页眉设置
        {
            "category": "页面设置",
            "name": "文档类型",
            "template_pattern": r"\\documentclass\[11pt\]\{article\}",
            "converter_pattern": r"\\\\documentclass\[11pt\]\{\{article\}\}",
            "expected": "11pt article"
        },
        {
            "category": "页面设置",
            "name": "纸张尺寸",
            "template_pattern": r"paperwidth=210mm, paperheight=285mm",
            "converter_pattern": r"paperwidth=210mm, paperheight=285mm",
            "expected": "210mm x 285mm"
        },
        {
            "category": "页面设置",
            "name": "页边距",
            "template_pattern": r"top=2\.5cm, bottom=2\.5cm, left=2cm, right=2cm",
            "converter_pattern": r"top=2\.5cm, bottom=2\.5cm, left=2cm, right=2cm",
            "expected": "top/bottom=2.5cm, left/right=2cm"
        },
        {
            "category": "页眉设置",
            "name": "第一页页眉间距",
            "template_pattern": r"\\setlength\{\\headsep\}\{4mm\}",
            "converter_pattern": r"\\\\setlength\{\{\\\\headsep\}\}\{\{4mm\}\}",
            "expected": "4mm"
        },
        {
            "category": "页眉设置",
            "name": "其他页页眉间距",
            "template_pattern": r"\\setlength\{\\headsep\}\{7mm\}",
            "converter_pattern": r"\\\\setlength\{\{\\\\headsep\}\}\{\{7mm\}\}",
            "expected": "7mm"
        },
        {
            "category": "页眉设置",
            "name": "页眉线宽度",
            "template_pattern": r"\\renewcommand\{\\headrulewidth\}\{0\.75pt\}",
            "converter_pattern": r"\\\\renewcommand\{\{\\\\headrulewidth\}\}\{\{0\.75pt\}\}",
            "expected": "0.75pt"
        },
        {
            "category": "页眉设置",
            "name": "页眉字体大小",
            "template_pattern": r"\\fontsize\{9pt\}\{10pt\}",
            "converter_pattern": r"\\\\fontsize\{\{9pt\}\}\{\{10pt\}\}",
            "expected": "9pt/10pt"
        },
        # 字体和颜色设置
        {
            "category": "字体设置",
            "name": "主字体",
            "template_pattern": r"Times New Roman",
            "converter_pattern": r"Times New Roman",
            "expected": "Times New Roman"
        },
        {
            "category": "颜色设置",
            "name": "黑色定义",
            "template_pattern": r"\\definecolor\{black\}\{HTML\}\{000000\}",
            "converter_pattern": r"\\\\definecolor\{\{black\}\}\{\{HTML\}\}\{\{000000\}\}",
            "expected": "#000000"
        },
        {
            "category": "颜色设置",
            "name": "背景色定义",
            "template_pattern": r"\\definecolor\{bgcolor\}\{HTML\}\{d8d8d8\}",
            "converter_pattern": r"\\\\definecolor\{\{bgcolor\}\}\{\{HTML\}\}\{\{d8d8d8\}\}",
            "expected": "#d8d8d8"
        },
        {
            "category": "颜色设置",
            "name": "蓝色定义",
            "template_pattern": r"\\definecolor\{blue\}\{HTML\}\{1F497D\}",
            "converter_pattern": r"\\\\definecolor\{\{blue\}\}\{\{HTML\}\}\{\{1F497D\}\}",
            "expected": "#1F497D"
        },
        # 段落设置
        {
            "category": "段落设置",
            "name": "首行缩进",
            "template_pattern": r"\\setlength\{\\parindent\}\{2em\}",
            "converter_pattern": r"\\\\setlength\{\{\\\\parindent\}\}\{\{2em\}\}",
            "expected": "2em"
        },
        {
            "category": "段落设置",
            "name": "段落间距",
            "template_pattern": r"\\setlength\{\\parskip\}\{0pt\}",
            "converter_pattern": r"\\\\setlength\{\{\\\\parskip\}\}\{\{0pt\}\}",
            "expected": "0pt"
        },
        # 标题格式设置
        {
            "category": "标题设置",
            "name": "一级标题字体",
            "template_pattern": r"\\fontsize\{15pt\}\{18\}\\bfseries",
            "converter_pattern": r"\\\\fontsize\{\{15pt\}\}\{\{18\}\}\\\\bfseries",
            "expected": "15pt/18pt bold"
        },
        {
            "category": "标题设置",
            "name": "一级标题间距",
            "template_pattern": r"\\titlespacing\*\{\\section\}\{0pt\}\{15pt\}\{10pt\}",
            "converter_pattern": r"\\\\titlespacing\*\{\{\\\\section\}\}\{\{0pt\}\}\{\{8pt\}\}\{\{6pt\}\}",
            "expected": "模板:15pt/10pt vs 转换:8pt/6pt",
            "allow_difference": True
        },
        # 表格设置
        {
            "category": "表格设置",
            "name": "表格行距",
            "template_pattern": r"\\renewcommand\{\\arraystretch\}\{1\}",
            "converter_pattern": r"\\\\renewcommand\{\{\\\\arraystretch\}\}\{\{1\}\}",
            "expected": "1"
        },
        {
            "category": "表格设置",
            "name": "表格标题上间距",
            "template_pattern": r"\\setlength\{\\abovecaptionskip\}\{3pt\}",
            "converter_pattern": r"\\\\setlength\{\{\\\\abovecaptionskip\}\}\{\{3pt\}\}",
            "expected": "3pt"
        },
        {
            "category": "表格设置",
            "name": "表格标题下间距",
            "template_pattern": r"\\setlength\{\\belowcaptionskip\}\{0pt\}",
            "converter_pattern": r"\\\\setlength\{\{\\\\belowcaptionskip\}\}\{\{0pt\}\}",
            "expected": "0pt"
        },
        # 特殊间距设置
        {
            "category": "间距设置",
            "name": "第一页顶部间距",
            "template_pattern": r"\\vspace\{2\.7mm\}",
            "converter_pattern": r"\\\\vspace\{\{2\.7mm\}\}",
            "expected": "2.7mm"
        }
    ]

    all_correct = True
    issues_found = []
    current_category = ""

    for check in checks:
        if check["category"] != current_category:
            current_category = check["category"]
            print(f"\n   📂 {current_category}:")

        template_match = re.search(check["template_pattern"], template_content)
        converter_match = re.search(check["converter_pattern"], converter_content)

        template_found = template_match is not None
        converter_found = converter_match is not None

        # 处理允许差异的情况
        if check.get("allow_difference", False):
            if template_found or converter_found:
                status = "⚠️"
                result = check["expected"]
            else:
                status = "❌"
                result = "两者都缺失"
                all_correct = False
                issues_found.append(f"{check['name']}: {result}")
        else:
            if template_found and converter_found:
                status = "✅"
                result = "一致"
            elif template_found and not converter_found:
                status = "❌"
                result = "转换程序中缺失"
                all_correct = False
                issues_found.append(f"{check['name']}: {result}")
            elif not template_found and converter_found:
                status = "⚠️"
                result = "模板中缺失，但转换程序中存在"
            else:
                status = "❌"
                result = "两者都缺失"
                all_correct = False
                issues_found.append(f"{check['name']}: {result}")

        print(f"      {status} {check['name']} ({check['expected']}): {result}")

    # 检查特殊的自定义命令
    print(f"\n   📂 自定义命令检查:")

    custom_commands = [
        ("articletype命令", r"\\newcommand\{\\articletype\}"),
        ("articletitle命令", r"\\newcommand\{\\articletitle\}"),
        ("articleabstract命令", r"\\newcommand\{\\articleabstract\}"),
        ("supernum命令", r"\\newcommand\{\\supernum\}")
    ]

    for cmd_name, pattern in custom_commands:
        template_has = re.search(pattern, template_content) is not None
        converter_has = re.search(pattern.replace("\\", "\\\\"), converter_content) is not None

        if template_has and converter_has:
            print(f"      ✅ {cmd_name}: 一致")
        elif template_has and not converter_has:
            print(f"      ❌ {cmd_name}: 转换程序中缺失")
            all_correct = False
            issues_found.append(f"{cmd_name}: 转换程序中缺失")
        elif not template_has and converter_has:
            print(f"      ⚠️ {cmd_name}: 模板中缺失，但转换程序中存在")
        else:
            print(f"      ❌ {cmd_name}: 两者都缺失")

    # 检查包导入
    print(f"\n   📂 关键包导入检查:")

    key_packages = [
        ("multicol", "双栏布局"),
        ("tcolorbox", "彩色框"),
        ("fontspec", "字体设置"),
        ("natbib", "参考文献"),
        ("booktabs", "表格线条"),
        ("caption", "标题设置")
    ]

    for pkg, desc in key_packages:
        template_has = f"\\usepackage{{{pkg}}}" in template_content or f"\\usepackage[" in template_content and f"]{{{pkg}}}" in template_content
        converter_has = f"\\\\usepackage{{{{{pkg}}}}}" in converter_content or (f"\\\\usepackage[" in converter_content and f"]{{{{{pkg}}}}}" in converter_content)

        if template_has and converter_has:
            print(f"      ✅ {pkg} ({desc}): 一致")
        elif template_has and not converter_has:
            print(f"      ❌ {pkg} ({desc}): 转换程序中缺失")
            all_correct = False
            issues_found.append(f"{pkg}包: 转换程序中缺失")
        else:
            print(f"      ⚠️ {pkg} ({desc}): 需要进一步检查")

    # 检查是否有被修改的设置
    print("\n🚨 检查是否有被擅自修改的设置:")

    modified_patterns = [
        (r"\\vspace\{1\.5mm\}", "1.5mm (应该是2.7mm)"),
        (r"\\setlength\{\\headsep\}\{2mm\}", "2mm (应该是4mm或7mm)"),
        (r"\\titlespacing\*\{\\section\}\{0pt\}\{8pt\}\{6pt\}", "section间距被修改")
    ]

    found_modifications = False
    for pattern, description in modified_patterns:
        if re.search(pattern, template_content):
            print(f"   ⚠️ 模板中发现修改: {description}")
            found_modifications = True
        if re.search(pattern.replace("\\", "\\\\"), converter_content):
            print(f"   ⚠️ 转换程序中发现修改: {description}")
            found_modifications = True

    if not found_modifications:
        print("   ✅ 未发现擅自修改的设置")

    # 总结
    print(f"\n📊 详细评估结果:")
    if all_correct:
        print("   ✅ 转换程序严格按照模板设置")
    else:
        print("   ❌ 发现以下不一致的设置:")
        for issue in issues_found:
            print(f"      • {issue}")

    return all_correct

def check_recent_outputs():
    """检查最近生成的输出文件是否使用了正确的设置"""
    print("\n🔍 检查最近生成的输出文件...")
    
    output_dirs = [
        "output_completely_fixed",
        "output_final_fixed", 
        "output_template_correct"
    ]
    
    for output_dir in output_dirs:
        output_file = Path(output_dir) / "output.tex"
        if output_file.exists():
            print(f"\n📄 检查 {output_file}:")
            
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键设置
            checks = [
                ("第一页页眉间距", r"\\setlength\{\\headsep\}\{4mm\}", "4mm"),
                ("其他页页眉间距", r"\\setlength\{\\headsep\}\{7mm\}", "7mm"),
                ("页眉线宽度", r"\\renewcommand\{\\headrulewidth\}\{0\.75pt\}", "0.75pt")
            ]
            
            for name, pattern, expected in checks:
                if re.search(pattern, content):
                    print(f"   ✅ {name}: {expected}")
                else:
                    print(f"   ❌ {name}: 未找到 {expected}")

if __name__ == "__main__":
    print("🚀 开始验证转换程序模板合规性...")
    
    compliance_ok = verify_template_compliance()
    check_recent_outputs()
    
    if compliance_ok:
        print("\n🎉 验证完成：转换程序严格按照模板设置！")
    else:
        print("\n⚠️ 验证完成：发现需要修正的问题！")

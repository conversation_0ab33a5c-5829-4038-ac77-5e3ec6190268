

%!!!Please use the XeLaTex typesetting engine!!!
\documentclass[10pt]{article} % 指定文档类型 [可以指定字号，纸张大小]{文档类型包括article,book,beamer(英文文档),ctexart,ctexbook,ctexbeamer(中文文档)等}
% 导言区: 全局设置, 宏包调用等。（以下部分是对全局有效果的区域）
% \usepackage[UTF8]{ctex}%中文排版加这个代码，否则不显示中文的文字(用在中英文结合的情况下,文档类型指定成英文的,加上这个可以在正文中显示中文部分)
% \usepackage[utf8]{inputenc}
\usepackage[paperwidth=210mm, paperheight=285mm, top=2.35cm, bottom=2.8cm, left=2cm, right=2cm]{geometry}%页面设置纸张类型和边距
\usepackage{fancyhdr}%页眉页脚的宏包
\usepackage[absolute, overlay]{textpos}
\usepackage{graphicx}
%\usepackage{amsmath} % AMS 数学公式 宏包 

\usepackage{unicode-math} % 导入 unicode-math 宏包
% 设置数学字体为 Cambria Math
%\setlength{\parindent}{2em}  %++++注销了这部分
%       % 你上传的正文字体文件名
%\usepackage{fontspec}
%\setmainfont[
%    Path = ./,
%    UprightFont = * ,
%    BoldFont = CAMBRIAB.TTF ,
%    ItalicFont = CAMBRIAI.TTF ,
%    BoldItalicFont = CAMBRIAZ.TTF
%]{Cambria.ttf}


\setlength{\parskip}{0pt} %++++设置段落之间的距离为0

%\usepackage{amssymb} % AMS 数学符号 宏包 
\usepackage{amsfonts} % AMS 数学字体 宏包
\usepackage{amsthm, bm} % 数学
\usepackage{xcolor}%颜色宏包
\usepackage{tcolorbox} % 导入 tcolorbox 宏包，用于创建带颜色背景的框
\usepackage{multicol} % 导入multicol宏包分栏！！
% \usepackage{microtype}
\usepackage{etoolbox}%参考文献自定义宏包
\usepackage{enumitem}
\usepackage{ragged2e} % 导入 ragged2e 宏包用于设置对齐
%字体相关宏包
\usepackage{titlesec}%标题自定义宏包
\usepackage{fontspec}
\usepackage{parskip}
\usepackage{titling}
\usepackage{lipsum} % 用于生成示例文本
\usepackage{setspace}%行间距相关宏包
%链接到文献地址宏包


\renewenvironment{proof}[1][\proofname]{\par %% \proofname allows to have "Proof of my theorem"
  \pushQED{\qed}%
  \normalfont \topsep6\p@\@plus6\p@\relax
  \trivlist
  \item[\hskip\labelsep
        \bfseries %% "Proof" is bold
    #1\@addpunct{.}]\ignorespaces %% Period instead of colon
}{%
  \popQED\endtrivlist\@endpefalse
}

\newcounter{theorem}
 \setcounter{theorem}{0}
 \newtheorem{Theorem}[theorem]{Theorem}
 
 \newcounter{lemma}
 \setcounter{lemma}{0}
 \newtheorem{Lemma}[lemma]{Lemma}
 
 \newcounter{corollary}
 \setcounter{corollary}{0}
 \newtheorem{Corollary}[corollary]{Corollary}
 
 \newcounter{proposition}
 \setcounter{proposition}{0}
 \newtheorem{Proposition}[proposition]{Proposition}
 
 \newcounter{characterization}
 \setcounter{characterization}{0}
 \newtheorem{Characterization}[characterization]{Characterization}
 
 \newcounter{property}
 \setcounter{property}{0}
 \newtheorem{Property}[property]{Property}
 
 \newcounter{problem}
 \setcounter{problem}{0}
 \newtheorem{Problem}[problem]{Problem}
 
 \newcounter{example}
 \setcounter{example}{0}
 \newtheorem{Example}[example]{Example}
 
 \newcounter{examplesanddefinitions}
 \setcounter{examplesanddefinitions}{0}
 \newtheorem{ExamplesandDefinitions}[examplesanddefinitions]{Examples and Definitions}
 
 \newcounter{remark}
 \setcounter{remark}{0}
 \newtheorem{Remark}[remark]{Remark}
 
 \newcounter{definition}
 \setcounter{definition}{0}
 \newtheorem{Definition}[definition]{Definition}
 
 \newcounter{hypothesis}
 \setcounter{hypothesis}{0}
 \newtheorem{Hypothesis}[hypothesis]{Hypothesis}

 \newcounter{notation}
 \setcounter{notation}{0}
 \newtheorem{Notation}[notation]{Notation}
 
 \newcounter{assumption}
 \setcounter{assumption}{0}
 \newtheorem{Assumption}[assumption]{Assumption}
 
 \newcounter{algorithm}
 \setcounter{algorithm}{0}
 \newtheorem{Algorithm}[algorithm]{Algorithm}
 
 % Define left/right mark in math environment
\let\originalleft\left
\let\originalright\right
\renewcommand{\left}{\mathopen{}\mathclose\bgroup\originalleft}
\renewcommand{\right}{\aftergroup\egroup\originalright}




%修改参考文献的数字的格式的
\usepackage[numbers,sort\&compress]{natbib}
%第一段首行缩进
\usepackage{indentfirst}
\usepackage{microtype}
\usepackage{titletoc}
\newenvironment{nospacing}
 {\par\setlength{\parskip}{0pt}} % 设置局部段落间距为0
 {\par}
%表格宏包
% 定义线条颜色
\definecolor{linecolor}{RGB}{200, 200, 200}
\usepackage{makecell}
\usepackage{caption}
\usepackage{booktabs}  % 用于创建漂亮的表格
\usepackage[colorlinks,linkcolor=blue,urlcolor=blue,citecolor=blue]{hyperref}
\urlstyle{same}%++++用于网址字体一致
\usepackage{url}%++++用于网址断行
\def\UrlBreaks{\do\A\do\B\do\C\do\D\do\E\do\F\do\G\do\H\do\I\do\J
\do\K\do\L\do\M\do\N\do\O\do\P\do\Q\do\R\do\S\do\T\do\U\do\V
\do\W\do\X\do\Y\do\Z\do\[\do\\\do\]\do\^\do\_\do\`\do\a\do\b
\do\c\do\d\do\e\do\f\do\g\do\h\do\i\do\j\do\k\do\l\do\m\do\n
\do\o\do\p\do\q\do\r\do\s\do\t\do\u\do\v\do\w\do\x\do\y\do\z
\do\.\do\@\do\\\do\/\do\!\do\_\do\|\do\;\do\>\do\]\do\)\do\,
\do\?\do\'\do+\do\=\do\#}
\def\UrlDigits{\do\1\do\2\do\3\do\4\do\5\do\6\do\7\do\8\do\9\do\0}


%++++list宏包
\setitemize{topsep=3pt,parsep=0pt,itemsep=0pt,leftmargin=*,labelsep=5.5mm,align=parleft}
\setenumerate{topsep=3pt,parsep=0pt,itemsep=0pt,leftmargin=*,labelsep=5.5mm,align=parleft}
\setlist[description]{itemsep=0mm}


\usepackage{tabularx}%表格通栏显示的宏
%\usepackage{multirow} % 注释掉，避免包缺失问题
\usepackage{float} % 引入 float 宏包，以便使用 [H] 参数
\renewcommand{\arraystretch}{1}%表格的行距
\newcolumntype{C}{>{\centering\arraybackslash}X} % 定义新的列类型 C

\setlength{\abovecaptionskip}{1em} % 设置标题与表格上方的距离

\captionsetup{
    justification=centering, % 设置标题居中对齐
    labelfont=bf, % 设置标签字体为粗体
    textfont=normalfont  % 设置标题文字为粗体
}
\captionsetup{labelsep=period} % 全局修改为点
%环境
%\newtheorem{theorem}{Theorem}%定理环境
%\newtheorem{corollary}{Corollary}%推论
%\newtheorem{definition}{Definition}%定义环境
%\newtheorem{lemma}{Lemma}%引理
%\numberwithin{equation}{section}%公式得是1,2,3,4,5

%定义proof样式
\makeatletter
\renewenvironment{proof}[1][\proofname]{\par
  \pushQED{\qed}%
  \normalfont\topsep6\p@\@plus6\p@\relax
  \trivlist
  \item[\hskip\labelsep
        \bfseries #1\@addpunct{.}]\ignorespaces
}{%
  \popQED\endtrivlist\@endpefalse
}
\makeatother

%以下为自定义部分公共样式-----------------
%1、自定义参考文献页面序数样式为1.、2.、而不是原生的[1]、[2]
\bibliographystyle{plainnat}
\makeatletter
\renewcommand\@biblabel[1]{#1.\hspace{1em}}
\makeatother

%这是设置注释的角标格式的
\setcitestyle{open={[},close={]},citesep={, }}
\bibliographystyle{plain}
%2、定义全文字体为Times New Roman
%\setmainfont{Times New Roman}
\setmainfont{Cambria}%++++修改字体为Cambria
%4 设置首行缩进为 2 字符
\setlength{\parindent}{2em}

%页眉设置开始------
% 首页的自定义页眉样式
\pagestyle{fancy}

  % 清除默认的页眉和页脚设置
  \fancyhf{}
  \renewcommand{\headrulewidth}{0.5pt} % 移除页眉的线
  \renewcommand{\headrule}{\color{black}\hrule width\headwidth height \headrulewidth} % 设置页眉线颜色
 \setlength{\headsep}{3mm} % 设置页眉线与正文之间的距离
  \setlength{\headheight}{29pt} % 设置页眉高度
  % 设置页眉内容
  \fancyhead[C]{
    \par\vspace{0.6pt}%段前
    {\noindent\fontsize{8.5pt}{9.6pt}\selectfont{\textit{\textbf{Journal Name}} | Volume xx | Issue xx}}%
    \par\vspace{-8pt}%段后
  }

  % 保留页脚的位置
% 在导言区设置
\fancypagestyle{firstpage}{%
    \fancyhf{} % 清除所有默认页眉页脚
  \fancyhead[C]{\par\vspace{0.6pt}%段前
     {\noindent\fontsize{9pt}{10pt}\selectfont{ \textit{\textbf{Journal Name}} | Volume xx | Issue xx }}\par\vspace{-12pt}%段后
    }
     \setlength{\headsep}{2mm} % 设置页眉线与正文之间的距离
    % 设置页脚内容
  \fancyfoot[L]{\noindent\fontsize{9pt}{10.8pt}\selectfont{https://doi.org/}}
  \fancyfoot[R]{\noindent\fontsize{9pt}{10.8pt}\selectfont\thepage}
\renewcommand{\footrulewidth}{0pt} % 页脚的线
\setcounter{page}{1}
}

\fancypagestyle{otherpages}{%
  \fancyfoot[C]{}
  \fancyfoot[R]{\noindent\fontsize{9pt}{10.8pt}\selectfont\thepage}
\renewcommand{\footrulewidth}{0pt} % 页脚的线
}
 

%页眉设置结束---------
%文章类型
\newcommand{\articletype}[1]{%
    \par\vspace{7.05pt}%段前
    {\noindent\fontsize{9pt}{10.8pt}\textit{#1} }%
    \par\vspace{7pt}%段后
}
%文章标题
\newcommand{\articletitle}[1]{%
    \par\vspace{0.2pt}%段前
    \noindent\parbox{\textwidth}{\fontsize{16pt}{19.2pt}\bfseries\textbf{#1} }% 居中对齐
    \par\vspace{0pt}%段后
}

%自定义颜色
\definecolor{black}{HTML}{000000} % 定义颜色
\definecolor{bgcolor}{HTML}{d8d8d8} % 定义颜色
\definecolor{blue}{HTML}{1F497D} % 定义颜色
\definecolor{red}{HTML}{FF0000} % 定义颜色
% 自定义链接
\newcommand{\ORCID}{https://orcid.org/0000-000X-XXXX-XXXX}
%模板应用自定义样式
\newcommand{\supernum}[1]{\textsuperscript{\bfseries{#1}}}

%自定标题样式
\renewcommand\refname{References}
%以下是常用格式
% 定义第一级标题的格式
\titleformat{\section}
  {\fontsize{12pt}{14.4pt}\bfseries\selectfont} % 设置字号为15pt，加粗
  {\thesection.} % 标题编号格式
  {0.5em} % 标题编号和标题文字的距离
  {} % 可以在这里添加额外的格式设定，比如颜色等
% 设置 section 标题的段前和段后间距
\titlespacing*{\section}{0pt}{1em}{0.5em} % 标题文字与正文间距


% 定义第二级标题的格式
\titleformat{\subsection}
  {\color{black}\bfseries\fontsize{11}{12.1}\selectfont}
  {\thesubsection.}
  {0.5em}
  {}
\titlespacing*{\subsection}{0pt}{1em}{0.5em}

% 定义第三级标题的格式
\titleformat{\subsubsection}
  {\color{black}\bfseries\fontsize{10}{12}\selectfont}
  {\thesubsubsection.}
  {0.5em}
  {}
\titlespacing*{\subsubsection}{0pt}{1em}{0.5em}

\newcommand{\subsubsubsection}[1]{%
    \par\vspace{5pt}%
    \noindent{\color{black}\fontsize{12}{14.4}\selectfont #1}%
    \par\vspace{5pt}%
}
% 重新定义公式编号格式
\renewcommand{\theequation}{\arabic{equation}}
%用来定义关键词下面的双线框
%\newcommand{\doublerule}{
  %  {\noindent\color{bgcolor}\rule{\linewidth}{0.25pt}}\\[-15pt]
  %  {\noindent\color{bgcolor}\rule{\linewidth}{0.25pt}}
%}
\usepackage{tcolorbox} % 导入 tcolorbox 宏包
\newcommand{\doublerule}{
    \begin{tcolorbox}[colback=white, colframe=bgcolor, arc=0mm, boxrule=1.05pt, width=\textwidth, height=0.6ex, left=0pt, right=0pt, top=0pt, bottom=0pt]
    \end{tcolorbox}
}
\begin{document}%{这个括号中是环境名称}
% 将第一页应用 firstpage 样式
\thispagestyle{firstpage}
% 从第二页开始应用 otherpages 样式
\pagestyle{otherpages}
\vspace{-13mm} % 设置合适的负值，减少页眉线与灰色框的距离
\noindent\textcolor{linecolor}{\rule{\textwidth}{0.45pt}} % 上方线条
\noindent\begin{minipage}[c]{3.69cm} % 左边部分占据三分之一的宽度
    % \vfill % 垂直填充
    \includegraphics[width=3.69cm]{logo.png} % 插入图片
    % \vfill % 垂直填充
\end{minipage}
\hfill % 用于分隔左右部分
\noindent\begin{minipage}[c]{\dimexpr\textwidth-3.69cm} % 右边部分占据三分之二的宽度
\vspace{-1pt}
    \begin{tcolorbox}[colback=bgcolor, colframe=gray!0!white, boxrule=0mm, arc=0mm, left=0mm, right=0mm, top=0mm, bottom=0mm, height=18mm]
        \centering{
            \fontsize{12pt}{14pt}\selectfont{\vspace{13pt}\textbf{Journal Name}\vspace{2.45mm}\\
            http://ojs.ukscip.com/index.php/xxxx\vspace{10pt}}
        }
    \end{tcolorbox}
\vspace{-2.5mm}
\end{minipage}
\noindent\textcolor{linecolor}{\rule{\textwidth}{1.55pt}} % 下方线条
\articletype{ Article Type (Article, Review, Communication or Editorial)}
\articletitle{Global Trends in Childhood Immunization Research: A
Bibliometric Analysis of Publications from 1974 to 2025}
%作者
\vspace{2pt} % 段前3磅

\noindent\fontsize{10pt}{12pt}\selectfont \textbf{
   Firstname Lastname \href{https://orcid.org/0000-000X-XXXX-XXXX}{\includegraphics[width=0.46cm]{ORCID.png}}, Firstname Lastname,  Firstname Lastname \supernum{*}\href{https://orcid.org/0000-000X-XXXX-XXXX}{\includegraphics[width=0.46cm]{ORCID.png}}
}

\vspace{0pt} % 段后5磅
%作者介绍
\noindent\fontsize{10pt}{15pt}\selectfont{$^{1}$ University Department, University Name, City State ZIP/Zone, Country}\\
\noindent\fontsize{10pt}{15pt}\selectfont{$^{2}$ Group, Laboratory, City State ZIP/Zone, Country}\\
\noindent\fontsize{10pt}{15pt}\selectfont{$^{*}$ Correspondence: <EMAIL>; Tel.: (optional; include country code; if there are multiple corresponding authors, add author initials)}
\vspace{3pt}
\begin{tcolorbox}[colback=bgcolor, colframe=black!1!black, boxrule=0.75pt, arc=0mm, left=2mm, right=2mm, top=1mm, bottom=0mm]
    \noindent\fontsize{10pt}{12pt}\selectfont{\textbf{Received:} Day Month Year; \textbf{Revised: }Day Month Year; \textbf{Accepted:} Day Month Year; \textbf{Published:} Day Month Year}
\end{tcolorbox}
% \articleabstract{ ABSTRACT }
\vspace{0.9em}
\noindent\fontsize{10}{13}\selectfont{\textbf{Abstract:} The abstract should state briefly the purpose of the research, the principal results, and major conclusions. An abstract is often presented separately from the article, so it must be able to stand alone. A concise and factual abstract is required. References should be avoided.}

\vspace{3pt}
{\noindent\fontsize{10}{13}\selectfont{\textbf{Keywords: }keyword 1; keyword 2; keyword 3 (Provide a maximum of 6 keywords, avoiding general and plural terms and multiple concepts (avoid, for example, ‘and’, ‘of’). Be sparing with abbreviations: only abbreviations firmly established in the field may be eligible. These keywords will be used for indexing purposes.}}

\vspace{15pt}
\doublerule
\vspace{3pt}

\section{Introduction}

The introduction should briefly place the study in a broad context and highlight why it is important, in particular, in relation to the current state of research in the field. Finally, it can conclude with a brief statement of the aim of the work and a comment about whether that aim was achieved \cite{1}.

\section{Materials and Methods}

The Materials and Methods should be described with sufficient details to allow others to replicate and build on the published results. Please note that the publication of your manuscript implicates that you must make all materials, data, computer code, and protocols associated with the publication available to readers \cite{2}. Please disclose at the submission stage any restrictions on the availability of materials or information. New methods and protocols should be described in detail while well-established methods can be briefly described and appropriately cited.\vspace{-5pt}

Research manuscripts reporting large datasets that are deposited in a publicly available database should specify where the data have been deposited and provide the relevant accession numbers \cite{3,4,5}. If the accession numbers have not yet been obtained at the time of submission, please state that they will be provided during review. They must be provided prior to publication.\vspace{-5pt}


Interventionary studies involving animals or humans, and other studies that require ethical approval, must list the authority that provided approval and the corresponding ethical approval code.



\section{Results}

Provide a concise and precise description of the experimental results, their interpretation as well as the experimental conclusions that can be drawn. 

\subsection{Subsection}

\subsubsection{Subsubsection}

Bulleted lists look like this:


\begin{itemize}[leftmargin=11mm]%Adjust the value according to the actual situation
\item	 First bullet;\vspace{-4pt}
\item	 Second bullet;\vspace{-4pt}
\item	 Third bullet.
\end{itemize}


Numbered lists can be added as follows:


\begin{enumerate}[leftmargin=12mm]%Adjust the value according to the actual situation
\item	First item; \vspace{-4pt}
\item	Second item;\vspace{-4pt}
\item	Third item.
\end{enumerate}


The text continues here.

\subsection{Figures, Tables and Schemes}

\textbf{Figures} include photographs, scanned images, graphs, charts and schematic diagrams. These captions should be numbered (e.g., Figure 1, Figure 2, etc.). All \textbf{Figures} and tables must have a brief title (also known as caption) that describes the entire figure without citing specific panels, followed by a legend, defined as a description of each panel. Please identify each panel with uppercase letters in parenthesis (e.g., (A), (B), (C), etc.) All \textbf{Figures} should be legible in print form and of optimal resolution \cite{6,7,8}.\vspace{-4pt}


All \textbf{Figures} and tables should be cited in the main text as \textbf{Figure 1}, \textbf{Table 1}, etc.


\begin{figure}[H]
\centering
\label{Figure 1.}
\includegraphics[width=0.25\linewidth]{image1.png}
\vspace{6pt}
\captionsetup{labelfont=bf, labelsep=period, justification=raggedright}
\caption{This is a figure. Schemes follow the same formatting.}
\end{figure}


\vspace{-8pt}
\begin{table}[H]

\caption{This is a table. Tables should be placed in the main text near to the first time they are cited.\label{tab1}}
%\vspace{3pt}
\small
\newcolumntype{C}{>{\centering\arraybackslash}X}
\begin{tabularx}{\textwidth}{CCC}
\toprule[0.5pt]
\textbf{Title 1} & \textbf{Title 2} & \textbf{Title 3} \\
\midrule[0.25pt]
entry 1 & data & data \\
entry 2 & data & data $^{1}$ \\
\bottomrule[0.5pt]
\end{tabularx}
\noindent{\footnotesize{\textsuperscript{1} Tables may have a footer.}}

\end{table}

\vspace{-8pt}
The text continues here (\textbf{Figure 2} and \textbf{Table 2} ).


\vspace{-6pt}
\begin{figure}[H]
\centering
\label{Figure 2.}
\begin{tabular}{cc}
\includegraphics[width=0.25\linewidth]{image2a.png}&\includegraphics[width=0.25\linewidth]{image2b.png}\\
(\textbf{a})&(\textbf{b})
\end{tabular}
\vspace{6pt}

\captionsetup{justification=justified,singlelinecheck=false}
\caption{This is a figure. Schemes follow another format. If there are multiple panels, they should be listed as: (\textbf{a}) Description of what is contained in the first panel; (\textbf{b}) Description of what is contained in the second panel. \textbf{Figures} should be placed in the main text near to the first time they are cited.}
\end{figure}


\vspace{-10pt}
\begin{table}[H]
%\captionsetup{justification=raggedright,singlelinecheck=false}
\caption{This is a table. Tables should be placed in the main text near to the first time they are cited.\label{tab2}}
\small
\newcolumntype{C}{>{\centering\arraybackslash}X}
\begin{tabularx}{\textwidth}{CCCC}
\toprule[0.5pt]
\textbf{Title 1}& \textbf{Title 2}& \textbf{Title 3}& \textbf{Title 4}\\
\midrule[0.25pt]
\multirow[m]{3}{*}{Entry 1 *}	& Data	& Data		& Data\\
    & Data		& Data	& Data\\
  & Data		& Data	& Data\\
 \midrule[0.25pt]
\multirow[m]{3}{*}{Entry 2}    & Data		& Data	& Data\\
	 & Data		& Data	& Data\\
	 & Data	& Data	& Data\\
\midrule[0.25pt]
\multirow[m]{3}{*}{Entry 3}    & Data	 & Data	& Data\\
     & Data	& Data	& Data\\
 & Data	& Data	& Data\\
 \midrule[0.25pt]
\multirow[m]{3}{*}{Entry 4}   & Data		& Data	& Data\\
     & Data & Data	& Data\\
	& Data	& Data	& Data\\
\bottomrule[0.5pt]
\end{tabularx}
\noindent{\footnotesize{* Tables may have a footer.}}
\end{table}



\subsection{Formatting of Mathematical Components}

This is the example 1 of equation:
\begin{equation}
\rm a = 1,
\end{equation}
the text following an equation need not be a new paragraph. Please punctuate equations as regular text.\vspace{-4pt}


This is the example 2 of equation:
\begin{equation}
\rm a =  b + c + d + e + f + g + h + i + j + k + l + m + n + o + p + q + r + s + t + u + v + w + x + y + z
\end{equation}
the text following an equation need not be a new paragraph. Please punctuate equations as regular text.\vspace{-4pt}

Theorem-type environments (including propositions, lemmas, corollaries etc.) can be formatted as follows:

\vspace{6pt}
%% Example of a theorem:
\begin{Theorem}
Example text of a theorem. Theorems, propositions, lemmas, etc. should be numbered sequentially (i.e., Proposition 2 follows Theorem 1). Examples or Remarks use the same formatting, but should be numbered separately, so a document may contain Theorem 1, Remark 1 and Example~1.
\end{Theorem}
\vspace{6pt}


The text continues here. Proofs must be formatted as follows:

\vspace{6pt}
%% Example of a proof:
\begin{proof}[Proof of Theorem 1]
Text of the proof. Note that the phrase “of Theorem 1” is optional if it is clear which theorem is being referred to. Always finish a proof with the following symbol.
\end{proof}
\vspace{6pt}


The text continues here.

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Discussion}

Authors should discuss the results and how they can be interpreted from the perspective of previous studies and of the working hypotheses. The findings and their implications should be discussed in the broadest context possible. Future research directions may also be highlighted.

\section{Conclusions}

This should clearly explain the main conclusions of the article, highlighting its importance and relevance. This section is not mandatory but can be added to the manuscript if the discussion is unusually long or complex.

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section{Patents}

This section is not mandatory, but may be added if there are patents resulting from the work reported in this manuscript.

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\vspace{6pt} 


\section*{Supplementary Materials}

Supplementary Material should be uploaded separately on submission.

\section*{Author Contributions}

For research articles with several authors, a short paragraph specifying their individual contributions must be provided. The following statements should be used “Conceptualization, X.X. and Y.Y.; methodology, X.X.; software, X.X.; validation, X.X., Y.Y. and Z.Z.; formal analysis, X.X.; investigation, X.X.; resources, X.X.; data curation, X.X.; writing—original draft preparation, X.X.; writing—review and editing, X.X.; visualization, X.X.; supervision, X.X.; project administration, X.X.; funding acquisition, Y.Y. All authors have read and agreed to the published version of the manuscript.” Authorship must be limited to those who have contributed substantially to the work reported.


\section*{Funding}

All sources of funding for the study should be disclosed. Clearly indicate grants that you have received in support of your research work and if you received funds to cover publication costs. Please add: “This work received no external funding” or This work was supported by [name of funder] grant number [xxx].

\section*{Institutional Review Board Statement}

In this section, you should add the Institutional Review Board Statement and approval number, if relevant to your study. You might choose to exclude this statement if the study did not require ethical approval. Please note that the Editorial Office might ask you for further information. Please add “The study was conducted in accordance with the Declaration of Helsinki, and approved by the Institutional Review Board (or Ethics Committee) of NAME OF INSTITUTE (protocol code XXX and date of approval).” for studies involving humans. OR “The animal study protocol was approved by the Institutional Review Board (or Ethics Committee) of NAME OF INSTITUTE (protocol code XXX and date of approval).” for studies involving animals. OR “Ethical review and approval were waived for this study due to REASON (please provide a detailed justification).” OR “Not applicable” for studies not involving humans or animals.

\section*{Informed Consent Statement}

Any research article describing a study involving humans should contain this statement. Please add “Informed consent was obtained from all subjects involved in the study.” OR “Patient consent was waived due to REASON (please provide a detailed justification).” OR “Not applicable.” for studies not involving humans. You might also choose to exclude this statement if the study did not involve humans.\vspace{-4pt}


Written informed consent for publication must be obtained from participating patients who can be identified (including by the patients themselves). Please state “Written informed consent has been obtained from the patient(s) to publish this paper” if applicable.

\section*{Data Availability Statement}

We encourage all authors of articles published in our journals to share their research data. In this section, please provide details regarding where data supporting reported results can be found, including links to publicly archived datasets analyzed or generated during the study. Where no new data were created, or where data is unavailable due to privacy or ethical restrictions, a statement is still required.


\section*{Acknowledgments}

In this section, you can acknowledge any support given which is not covered by the author contribution or funding sections. This may include administrative and technical support, or donations in kind (e.g., materials used for experiments).




\section*{Confict of Interest}

Declare conflicts of interest or state “The authors declare no conflict of interest.” Authors must identify and declare any personal circumstances or interest that may be perceived as inappropriately influencing the representation or interpretation of reported research results. Any role of the funders in the design of the study; in the collection, analyses or interpretation of data; in the writing of the manuscript; or in the decision to publish the results must be declared in this section. If there is no role, please state “The funders had no role in the design of the study; in the collection, analyses, or interpretation of data; in the writing of the manuscript; or in the decision to publish the results”.


\section*{Appendix  A}
The appendix is an optional section that can contain details and data supplemental to the main text---for example, explanations of experimental details that would disrupt the flow of the main text but nonetheless remain crucial to understanding and reproducing the research shown; \textbf{Figures} of replicates for experiments of which representative data are shown in the main text can be added here if brief, or as Supplementary Data. Mathematical proofs of results not central to the paper can be added as an appendix.


\section*{Appendix  B}
All appendix sections must be cited in the main text. In the appendices, \textbf{Figures}, Tables, etc. should be labeled, starting with ``A''---e.g., Figure A1, Figure A2, etc.



\vspace{6pt}
\begin{thebibliography}{99}
    \singlespacing
    \setlength{\itemsep}{0pt}  % 去除条目之间的垂直间距
    %\setlength{\parsep}{3pt}  % 去除段落之间的垂直间距
    %\vspace{-\baselineskip}
    \providecommand{\doi}[1]{doi: #1}
\vspace{-12pt}

%ZY: References must be numbered in order of appearance in the text (including citations in tables and legends) and listed individually at the end of the manuscript. We recommend preparing the references with a bibliography software package, such as EndNote, Reference Manager or Zotero to avoid typing mistakes and duplicated references. Include the digital object identifier (DOI) for all references where available.
%
%ZY: Citations and references in the Supplementary Materials are permitted provided that they also appear in the reference list here. 
%
%ZY: In the text, reference numbers should be placed in square brackets [ ] and placed before the punctuation; for example [1], [1–3] or [1,3]. For embedded citations in the text with pagination, use both parentheses and brackets to indicate the reference number and page numbers; for example [5] (p. 10), or [6] (pp. 101–105).



\bibitem{1} Author 1, A.B.; Author 2, C.D. Title of The Article. \emph{Abbreviated Journal Name} \textbf{Year}, \emph{Volume}, page range.

\bibitem{2} Author 1, A.; Author 2, B. Title of the Chapter. In \emph{Book Title}, 2nd ed.; Editor 1, A., Editor 2, B., Eds.; Publisher: Publisher Location, Country, year; Volume, pp. page range.

\bibitem{3} Author 1, A.; Author 2, B. \emph{Book Title}, 3rd ed.; Publisher: Publisher Location, Country, year; pp. page range.

\bibitem{4} Author 1, A.B.; Author 2, C. Title of Unpublished Work. \emph{Abbreviated Journal Name} year, \emph{phrase indicating stage of publication (submitted; accepted; in press)}.

\bibitem{5} Author 1, A.B. (University, City, State, Country); Author 2, C. (Institute, City, State, Country). Personal Communication, year.

\bibitem{6} Author 1, A.B.; Author 2, C.D.; Author 3, E.F. Title of Presentation. In Proceedings of the Name of the Conference, Location of Conference, Country, Date of Conference (Day Month Year).

\bibitem{7} Author 1, A.B. Title of Thesis. Level of Thesis, Degree-Granting University, Location of University, Date of Completion.

\bibitem{8} Title of Site. Available online: URL (accessed on Day Month Year).




\end{thebibliography}
\vspace{3pt}

\noindent\begin{minipage}[c]{1.92cm} % 左边部分占据三分之一的宽度
    % \vfill % 垂直填充
    \includegraphics[width=1.92cm]{cc.png} % 插入图片
    % \vfill % 垂直填充
\end{minipage}
\hfill % 用于分隔左右部分
\noindent\begin{minipage}[c]{\dimexpr\textwidth-1.92cm} % 右边部分占据三分之二的宽度
    \begin{tcolorbox}[colback=white!0!white, colframe=gray!0!white, boxrule=0mm, arc=0mm, left=0mm, right=0mm, top=0mm, bottom=0mm]
        \fontsize{9pt}{10.8pt}\selectfont{Copyright © 2024 by the author(s). Published by UK Scientific Publishing Limited. This is an open access article under the Creative Commons Attribution (CC BY) license (https://creativecommons.org/licenses/by/4.0/).}
    \end{tcolorbox}
\end{minipage}

\vspace{4pt} 
%%%% Publisher's note
\noindent\fontsize{9pt}{9pt}\selectfont{Publisher’s Note: The views, opinions, and information presented in all publications are the sole responsibility of the respective authors and contributors, and do not necessarily reflect the views of UK Scientific Publishing Limited and/or its editors. UK Scientific Publishing Limited and/or its editors hereby disclaim any liability for any harm or damage to individuals or property arising from the implementation of ideas, methods, instructions, or products mentioned in the content.}


\end{document}


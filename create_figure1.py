#!/usr/bin/env python3
"""
创建Figure 1 - Research Framework图片
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_figure1():
    """创建Figure 1 - Research Framework的流程图"""
    
    # 创建主画布 (1000x600像素)
    width, height = 1000, 600
    img = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(img)
    
    try:
        font_large = ImageFont.truetype("arial.ttf", 14)
        font_medium = ImageFont.truetype("arial.ttf", 12)
        font_small = ImageFont.truetype("arial.ttf", 10)
    except:
        font_large = ImageFont.load_default()
        font_medium = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # 定义颜色
    box_color = "lightblue"
    border_color = "darkblue"
    text_color = "black"
    arrow_color = "darkblue"
    
    # 绘制研究框架的步骤框
    steps = [
        {"text": "1. Analyze the needs of\nBuriram Airport personnel", "x": 150, "y": 50, "w": 200, "h": 60},
        {"text": "2. Design E-book and\naudio file content", "x": 450, "y": 50, "w": 200, "h": 60},
        {"text": "3. Develop E-book with\nQR code integration", "x": 750, "y": 50, "w": 200, "h": 60},
        {"text": "4. Create audio files for\neach dialogue unit", "x": 150, "y": 200, "w": 200, "h": 60},
        {"text": "5. Integrate QR codes\nfor audio access", "x": 450, "y": 200, "w": 200, "h": 60},
        {"text": "6. Test with sample\ngroup of personnel", "x": 750, "y": 200, "w": 200, "h": 60},
        {"text": "7. Evaluate innovation\nwith expert panel", "x": 300, "y": 350, "w": 200, "h": 60},
        {"text": "8. Finalize E-book and\naudio file system", "x": 600, "y": 350, "w": 200, "h": 60},
        {"text": "Complete Innovation:\nE-book + Audio Files", "x": 400, "y": 500, "w": 200, "h": 60}
    ]
    
    # 绘制步骤框
    for step in steps:
        # 绘制矩形框
        draw.rectangle([step["x"], step["y"], step["x"] + step["w"], step["y"] + step["h"]], 
                      fill=box_color, outline=border_color, width=2)
        
        # 绘制文本
        lines = step["text"].split('\n')
        text_y = step["y"] + 10
        for line in lines:
            text_bbox = draw.textbbox((0, 0), line, font=font_medium)
            text_width = text_bbox[2] - text_bbox[0]
            text_x = step["x"] + (step["w"] - text_width) // 2
            draw.text((text_x, text_y), line, fill=text_color, font=font_medium)
            text_y += 20
    
    # 绘制箭头连接
    arrows = [
        # 第一行的箭头
        {"start": (350, 80), "end": (450, 80)},
        {"start": (650, 80), "end": (750, 80)},
        # 向下的箭头
        {"start": (250, 110), "end": (250, 200)},
        {"start": (550, 110), "end": (550, 200)},
        {"start": (850, 110), "end": (850, 200)},
        # 第二行的箭头
        {"start": (350, 230), "end": (450, 230)},
        {"start": (650, 230), "end": (750, 230)},
        # 向下到评估
        {"start": (250, 260), "end": (300, 350)},
        {"start": (550, 260), "end": (500, 350)},
        {"start": (850, 260), "end": (700, 350)},
        # 评估到最终
        {"start": (400, 410), "end": (500, 500)},
        {"start": (700, 410), "end": (600, 500)}
    ]
    
    # 绘制箭头
    for arrow in arrows:
        start_x, start_y = arrow["start"]
        end_x, end_y = arrow["end"]
        
        # 绘制箭头线
        draw.line([start_x, start_y, end_x, end_y], fill=arrow_color, width=2)
        
        # 绘制箭头头部
        if end_x > start_x:  # 向右箭头
            draw.polygon([(end_x, end_y), (end_x-8, end_y-4), (end_x-8, end_y+4)], fill=arrow_color)
        elif end_x < start_x:  # 向左箭头
            draw.polygon([(end_x, end_y), (end_x+8, end_y-4), (end_x+8, end_y+4)], fill=arrow_color)
        elif end_y > start_y:  # 向下箭头
            draw.polygon([(end_x, end_y), (end_x-4, end_y-8), (end_x+4, end_y-8)], fill=arrow_color)
        else:  # 向上箭头
            draw.polygon([(end_x, end_y), (end_x-4, end_y+8), (end_x+4, end_y+8)], fill=arrow_color)
    
    # 添加标题
    title = "Research Framework for English Communication Innovation"
    title_bbox = draw.textbbox((0, 0), title, font=font_large)
    title_width = title_bbox[2] - title_bbox[0]
    title_x = (width - title_width) // 2
    draw.text((title_x, 10), title, fill=text_color, font=font_large)
    
    # 保存图片
    output_dir = "output_final_complete_fixed/images"
    os.makedirs(output_dir, exist_ok=True)
    img.save(f"{output_dir}/figure1_research_framework.png", "PNG")
    print("Figure 1 created successfully!")

if __name__ == "__main__":
    create_figure1()

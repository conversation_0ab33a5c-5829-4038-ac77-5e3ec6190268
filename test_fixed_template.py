#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的模板效果
"""

from pathlib import Path
from docx_to_latex_correct import CorrectDocxToLatexConverter
import shutil
import subprocess
import sys

def test_fixed_template():
    """测试修复后的模板效果"""
    print("🧪 测试修复后的模板效果...")
    
    # 输入文件
    input_docx = Path("实例文档/双栏案列/FLS 8565 - 原始版本.docx")
    output_dir = Path("output_template_fixed")
    
    # 清理并创建输出目录
    if output_dir.exists():
        shutil.rmtree(output_dir)
    output_dir.mkdir(exist_ok=True)
    
    if not input_docx.exists():
        print(f"❌ 找不到输入文件: {input_docx}")
        print("请确保文件路径正确")
        return False
    
    try:
        # 创建转换器
        converter = CorrectDocxToLatexConverter(input_docx, output_dir)
        
        print(f"📄 正在转换文件: {input_docx}")
        
        # 转换为LaTeX
        tex_file = converter.convert_to_latex()
        
        if tex_file:
            print(f"✅ LaTeX文件生成成功: {tex_file}")
            
            # 检查生成的LaTeX文件中的页眉设置
            with open(tex_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print("\n🔍 检查生成的LaTeX文件中的页眉设置:")
            
            # 检查我们的转换器生成的设置
            if "\\setlength{\\headsep}{2mm}" in content:
                print("   ✅ 转换器设置的headsep为2mm")
            else:
                print("   ❌ 转换器未正确设置headsep")
            
            # 检查是否有注释的headheight
            if "% \\setlength{\\headheight}{25pt}" in content:
                print("   ✅ headheight已正确注释")
            else:
                print("   ❌ headheight未正确注释")
            
            # 编译为PDF
            print("\n🔄 正在编译PDF...")
            success = compile_latex_to_pdf(tex_file)
            
            if success:
                pdf_file = tex_file.with_suffix('.pdf')
                if pdf_file.exists():
                    print(f"🎉 PDF生成成功: {pdf_file}")
                    print(f"📊 PDF文件大小: {pdf_file.stat().st_size / 1024:.1f} KB")
                    return True
                else:
                    print("❌ PDF文件未找到")
                    return False
            else:
                print("❌ PDF编译失败")
                return False
        else:
            print("❌ LaTeX文件生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 转换过程中出现错误: {e}")
        return False

def compile_latex_to_pdf(tex_file: Path) -> bool:
    """编译LaTeX为PDF"""
    try:
        # 切换到输出目录
        original_dir = Path.cwd()
        output_dir = tex_file.parent
        
        # 使用XeLaTeX编译
        cmd = ["xelatex", "-interaction=nonstopmode", tex_file.name]
        
        print(f"执行命令: {' '.join(cmd)}")
        print(f"工作目录: {output_dir}")
        
        # 编译两次以确保引用正确
        for i in range(2):
            result = subprocess.run(
                cmd,
                cwd=output_dir,
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            
            if result.returncode != 0:
                print(f"❌ 第{i+1}次编译失败")
                print("错误输出:")
                print(result.stderr)
                return False
            else:
                print(f"✅ 第{i+1}次编译成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 编译过程中出现错误: {e}")
        return False

def compare_with_previous():
    """与之前的版本对比"""
    print("\n📊 对比分析:")
    
    # 对比不同版本的PDF
    versions = [
        ("原始版本", Path("output_completely_fixed/output.pdf")),
        ("修复版本", Path("output_template_fixed/output.pdf")),
    ]
    
    print("📋 PDF文件大小对比:")
    for name, pdf_path in versions:
        if pdf_path.exists():
            size = pdf_path.stat().st_size / 1024
            print(f"   {name}: {size:.1f} KB")
        else:
            print(f"   {name}: 文件不存在")
    
    print("\n💡 修复说明:")
    print("   - 原始模板中的页眉间距已从4mm/7mm减小到2mm")
    print("   - 页面顶部间距从2.7mm减小到1.5mm")
    print("   - 关键词后间距从9mm减小到6mm")
    print("   - 这些修改将显著减少页眉到正文的距离")

def check_template_status():
    """检查模板文件状态"""
    print("\n🔍 检查模板文件状态:")
    
    template_file = Path("双栏latex修改案例(1)/双语.tex")
    backup_file = Path("双栏latex修改案例(1)/双语_backup.tex")
    
    if template_file.exists():
        print(f"   ✅ 主模板文件存在: {template_file}")
        
        # 检查是否已修复
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "\\setlength{\\headsep}{2mm}" in content:
            print("   ✅ 模板已修复 (headsep = 2mm)")
        else:
            print("   ❌ 模板未修复")
    else:
        print(f"   ❌ 主模板文件不存在: {template_file}")
    
    if backup_file.exists():
        print(f"   ✅ 备份文件存在: {backup_file}")
    else:
        print(f"   ❌ 备份文件不存在: {backup_file}")

def main():
    """主函数"""
    print("🎯 测试修复后的模板效果")
    print("=" * 50)
    
    # 检查模板状态
    check_template_status()
    
    # 测试转换
    success = test_fixed_template()
    
    if success:
        compare_with_previous()
        print("\n🎉 测试完成！")
        print("\n📝 总结:")
        print("   1. 原始模板文件已修复页眉间距问题")
        print("   2. 转换器生成的LaTeX文件使用正确的间距设置")
        print("   3. PDF生成成功，页眉到正文的距离已减小")
        print("\n📁 输出文件位置: output_template_fixed/")
    else:
        print("\n❌ 测试失败！")

if __name__ == "__main__":
    main()

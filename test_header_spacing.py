#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试页眉间距修复效果
"""

from pathlib import Path
from docx_to_latex_correct import CorrectDocxToLatexConverter
import shutil
import subprocess
import sys

def test_header_spacing():
    """测试页眉间距修复"""
    print("🔧 测试页眉间距修复效果...")
    
    # 输入文件
    input_docx = Path("实例文档/双栏案列/FLS 8565 - 原始版本.docx")
    output_dir = Path("output_header_fixed")
    
    # 清理并创建输出目录
    if output_dir.exists():
        shutil.rmtree(output_dir)
    output_dir.mkdir(exist_ok=True)
    
    if not input_docx.exists():
        print(f"❌ 找不到输入文件: {input_docx}")
        print("请确保文件路径正确")
        return False
    
    try:
        # 创建转换器
        converter = CorrectDocxToLatexConverter(input_docx, output_dir)
        
        print(f"📄 正在转换文件: {input_docx}")
        
        # 转换为LaTeX
        tex_file = converter.convert_to_latex()
        
        if tex_file:
            print(f"✅ LaTeX文件生成成功: {tex_file}")
            
            # 检查页眉设置
            with open(tex_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print("\n🔍 检查页眉设置:")
            if "% \\setlength{\\headheight}{25pt}" in content:
                print("   ✅ headheight已注释")
            else:
                print("   ❌ headheight未正确注释")
            
            if "\\setlength{\\headsep}{2mm}" in content:
                print("   ✅ headsep已设置为2mm")
            else:
                print("   ❌ headsep未正确设置")
            
            # 编译为PDF
            print("\n🔄 正在编译PDF...")
            success = compile_latex_to_pdf(tex_file)
            
            if success:
                pdf_file = tex_file.with_suffix('.pdf')
                if pdf_file.exists():
                    print(f"🎉 PDF生成成功: {pdf_file}")
                    print(f"📊 PDF文件大小: {pdf_file.stat().st_size / 1024:.1f} KB")
                    print("\n📋 请检查生成的PDF，页眉到正文的距离应该已经减小")
                    return True
                else:
                    print("❌ PDF文件未找到")
                    return False
            else:
                print("❌ PDF编译失败")
                return False
        else:
            print("❌ LaTeX文件生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 转换过程中出现错误: {e}")
        return False

def compile_latex_to_pdf(tex_file: Path) -> bool:
    """编译LaTeX为PDF"""
    try:
        # 切换到输出目录
        original_dir = Path.cwd()
        output_dir = tex_file.parent
        
        # 使用XeLaTeX编译
        cmd = ["xelatex", "-interaction=nonstopmode", tex_file.name]
        
        print(f"执行命令: {' '.join(cmd)}")
        print(f"工作目录: {output_dir}")
        
        # 编译两次以确保引用正确
        for i in range(2):
            result = subprocess.run(
                cmd,
                cwd=output_dir,
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            
            if result.returncode != 0:
                print(f"❌ 第{i+1}次编译失败")
                print("错误输出:")
                print(result.stderr)
                return False
            else:
                print(f"✅ 第{i+1}次编译成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 编译过程中出现错误: {e}")
        return False

def compare_with_original():
    """与原始版本对比"""
    print("\n📊 对比分析:")
    
    original_pdf = Path("output_completely_fixed/output.pdf")
    new_pdf = Path("output_header_fixed/output.pdf")
    
    if original_pdf.exists() and new_pdf.exists():
        original_size = original_pdf.stat().st_size / 1024
        new_size = new_pdf.stat().st_size / 1024
        
        print(f"   原始版本PDF: {original_size:.1f} KB")
        print(f"   修复版本PDF: {new_size:.1f} KB")
        print(f"   大小差异: {abs(new_size - original_size):.1f} KB")
        
        print("\n💡 建议:")
        print("   1. 打开两个PDF文件进行对比")
        print("   2. 重点检查页眉到正文的距离")
        print("   3. 确认整体布局是否更加紧凑")
    else:
        print("   ⚠️ 无法找到对比文件")

if __name__ == "__main__":
    success = test_header_spacing()
    if success:
        compare_with_original()
        print("\n🎯 页眉间距修复测试完成！")
        print("📝 主要修改:")
        print("   - 注释掉了 \\headheight 设置")
        print("   - 将 \\headsep 从 4mm/7mm 减小到 2mm")
        print("   - 这应该显著减少页眉到正文的距离")
    else:
        print("\n❌ 页眉间距修复测试失败！")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DOCX到LaTeX转换程序
"""

from pathlib import Path
from docx_to_latex_correct import CorrectDocxToLatexConverter, compile_latex_to_pdf
import shutil

def test_conversion():
    """测试转换功能"""
    print("🚀 开始测试DOCX到LaTeX转换程序...")
    
    # 输入文件
    input_docx = Path("实例文档/双栏案列/FLS 8565 - 原始版本.docx")
    output_dir = Path("test_output")
    
    # 清理并创建输出目录
    if output_dir.exists():
        shutil.rmtree(output_dir)
    output_dir.mkdir(exist_ok=True)
    
    try:
        # 创建转换器
        converter = CorrectDocxToLatexConverter(input_docx, output_dir)
        
        print(f"📄 正在转换文件: {input_docx}")
        
        # 转换为LaTeX
        tex_file = converter.convert_to_latex()
        
        if tex_file:
            print(f"✅ LaTeX文件生成成功: {tex_file}")
            
            # 编译为PDF
            print("🔄 正在编译PDF...")
            compile_latex_to_pdf(tex_file)
            
            # 检查PDF是否生成
            pdf_file = tex_file.with_suffix('.pdf')
            if pdf_file.exists():
                print(f"🎉 PDF生成成功: {pdf_file}")
                print(f"📊 PDF文件大小: {pdf_file.stat().st_size / 1024:.1f} KB")
                
                # 对比文件
                original_pdf = Path("实例文档/双栏案列/FLS 8565 -最终.pdf")
                if original_pdf.exists():
                    original_size = original_pdf.stat().st_size / 1024
                    generated_size = pdf_file.stat().st_size / 1024
                    print(f"📋 原始PDF大小: {original_size:.1f} KB")
                    print(f"📋 生成PDF大小: {generated_size:.1f} KB")
                    print(f"📋 大小比例: {generated_size/original_size:.2f}")
                
                return True
            else:
                print("❌ PDF生成失败")
                return False
        else:
            print("❌ LaTeX文件生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 转换过程中出现错误: {e}")
        return False

def analyze_conversion_quality():
    """分析转换质量"""
    print("\n📊 分析转换质量...")
    
    tex_file = Path("test_output/output.tex")
    if not tex_file.exists():
        print("❌ 找不到生成的LaTeX文件")
        return
    
    with open(tex_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 统计信息
    stats = {
        "总行数": len(content.split('\n')),
        "图片数量": content.count('\\includegraphics'),
        "表格数量": content.count('\\begin{table}'),
        "章节数量": content.count('\\section{'),
        "子章节数量": content.count('\\subsection{'),
        "双栏环境": content.count('\\begin{multicols}{2}'),
        "跨栏图片": content.count('\\end{multicols}\\n\\begin{figure}'),
    }
    
    print("📈 转换统计:")
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    # 检查关键特征
    print("\n🔍 关键特征检查:")
    checks = {
        "双栏布局": "\\begin{multicols}{2}" in content,
        "图片处理": "\\includegraphics" in content,
        "表格处理": "\\begin{table}" in content,
        "字体设置": "Times New Roman" in content,
        "页眉页脚": "\\fancyhead" in content,
        "参考文献": "\\begin{thebibliography}" in content,
    }
    
    for feature, present in checks.items():
        status = "✅" if present else "❌"
        print(f"   {status} {feature}")

if __name__ == "__main__":
    success = test_conversion()
    if success:
        analyze_conversion_quality()
        print("\n🎯 转换测试完成！请检查生成的PDF文件与原始PDF的对比效果。")
    else:
        print("\n❌ 转换测试失败！")

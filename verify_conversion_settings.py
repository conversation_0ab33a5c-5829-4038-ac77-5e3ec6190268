#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证转换程序的设置是否正确
"""

from pathlib import Path
import re

def verify_latex_settings():
    """验证生成的LaTeX文件中的关键设置"""
    
    tex_file = Path("test_output/output.tex")
    if not tex_file.exists():
        print("❌ 找不到生成的LaTeX文件")
        return False
    
    with open(tex_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🔍 验证LaTeX设置...")
    
    # 检查关键设置
    checks = {
        "文档类": r"\\documentclass\[11pt\]\{article\}",
        "页面设置": r"paperwidth=210mm, paperheight=285mm",
        "页眉高度": r"\\setlength\{\\headheight\}\{25pt\}",
        "第一页页眉间距": r"\\setlength\{\\headsep\}\{4mm\}",
        "其他页页眉间距": r"\\setlength\{\\headsep\}\{7mm\}",
        "第一页额外间距": r"\\vspace\{2\.7mm\}",
        "Times New Roman字体": r"\\setmainfont\{Times New Roman\}",
        "双栏环境": r"\\begin\{multicols\}\{2\}",
        "页眉内容": r"Forum for Linguistic Studies",
        "图片包含": r"\\includegraphics",
        "表格环境": r"\\begin\{table\}",
        "参考文献": r"\\begin\{thebibliography\}",
    }
    
    results = {}
    for name, pattern in checks.items():
        matches = re.findall(pattern, content)
        results[name] = len(matches) > 0
        status = "✅" if results[name] else "❌"
        count = len(matches) if matches else 0
        print(f"   {status} {name}: {count} 个匹配")
    
    # 特殊检查：确保第一页和其他页的设置都存在
    first_page_settings = re.search(r"\\fancypagestyle\{first\}\{.*?\\vspace\{2\.7mm\}.*?\}", content, re.DOTALL)
    other_page_settings = re.search(r"\\fancypagestyle\{other\}\{.*?\\setlength\{\\headsep\}\{7mm\}.*?\}", content, re.DOTALL)
    
    print(f"\n📋 页面样式检查:")
    print(f"   {'✅' if first_page_settings else '❌'} 第一页样式完整 (包含2.7mm间距)")
    print(f"   {'✅' if other_page_settings else '❌'} 其他页样式完整 (包含7mm间距)")
    
    # 统计信息
    print(f"\n📊 内容统计:")
    print(f"   总行数: {len(content.split(chr(10)))}")
    print(f"   图片数量: {len(re.findall(r'\\includegraphics', content))}")
    print(f"   表格数量: {len(re.findall(r'\\begin\{table\}', content))}")
    print(f"   双栏环境: {len(re.findall(r'\\begin\{multicols\}\{2\}', content))}")
    
    # 检查是否有明显的错误
    errors = []
    if "&" in content and "\\&" not in content:
        errors.append("可能存在未转义的&符号")
    
    if errors:
        print(f"\n⚠️ 发现潜在问题:")
        for error in errors:
            print(f"   - {error}")
    
    # 总体评估
    passed = sum(results.values())
    total = len(results)
    success_rate = passed / total * 100
    
    print(f"\n🎯 验证结果: {passed}/{total} 项通过 ({success_rate:.1f}%)")
    
    if success_rate >= 90:
        print("✅ 转换设置验证通过！")
        return True
    else:
        print("❌ 转换设置需要改进")
        return False

if __name__ == "__main__":
    verify_latex_settings()

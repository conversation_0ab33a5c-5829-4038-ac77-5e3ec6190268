This is XeTeX, Version 3.141592653-2.6-0.999997 (TeX Live 2025) (preloaded format=xelatex 2025.7.23)  31 JUL 2025 13:10
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**output.tex
(./output.tex
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-07-19>
(c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/base/article.cls
Document Class: article 2025/01/22 v1.4n Standard LaTeX document class
(c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2025/01/22 v1.4n Standard LaTeX file (size option)
)
\c@part=\count271
\c@section=\count272
\c@subsection=\count273
\c@subsubsection=\count274
\c@paragraph=\count275
\c@subparagraph=\count276
\c@figure=\count277
\c@table=\count278
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen148
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2024/12/31 v1.2e Enhanced LaTeX Graphics (DPC,SPQR)
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 106.
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen149
\Gin@req@width=\dimen150
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count279
\Gm@cntv=\count280
\c@Gm@tempcnt=\count281
\Gm@bindingoffset=\dimen151
\Gm@wd@mp=\dimen152
\Gm@odd@mp=\dimen153
\Gm@even@mp=\dimen154
\Gm@layoutwidth=\dimen155
\Gm@layoutheight=\dimen156
\Gm@layouthoffset=\dimen157
\Gm@layoutvoffset=\dimen158
\Gm@dimlist=\toks18
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2025/02/07 v5.2 Extensive control of page headers and footers
\f@nch@headwidth=\skip51
\f@nch@offset@elh=\skip52
\f@nch@offset@erh=\skip53
\f@nch@offset@olh=\skip54
\f@nch@offset@orh=\skip55
\f@nch@offset@elf=\skip56
\f@nch@offset@erf=\skip57
\f@nch@offset@olf=\skip58
\f@nch@offset@orf=\skip59
\f@nch@height=\skip60
\f@nch@footalignment=\skip61
\f@nch@widthL=\skip62
\f@nch@widthC=\skip63
\f@nch@widthR=\skip64
\@temptokenb=\toks19
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/textpos/textpos.sty
Package: textpos 2022/07/23 v1.10.1
Package textpos Info: choosing support for LaTeX3 on input line 60.
\TP@textbox=\box53
\TP@holdbox=\box54
\TPHorizModule=\dimen159
\TPVertModule=\dimen160
\TP@margin=\dimen161
\TP@absmargin=\dimen162

Grid set 16 x 16 = 37.34424pt x 50.68146pt
\TPboxrulesize=\dimen163
\TP@ox=\dimen164
\TP@oy=\dimen165
\TP@tbargs=\toks20
TextBlockOrigin set to 0pt x 0pt
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/caption/subcaption.sty
Package: subcaption 2023/07/28 v1.6b Sub-captions (AR)
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen166
\captionmargin=\dimen167
\caption@leftmargin=\dimen168
\caption@rightmargin=\dimen169
\caption@width=\dimen170
\caption@indent=\dimen171
\caption@parindent=\dimen172
\caption@hangindent=\dimen173
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count282
\c@continuedfloat=\count283
)
Package caption Info: New subtype `subfigure' on input line 238.
\c@subfigure=\count284
Package caption Info: New subtype `subtable' on input line 238.
\c@subtable=\count285
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2025/06/16 v2.17y AMS math features
\@mathmargin=\skip65

For additional information on amsmath, use the `?' option.
(c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2024/11/17 v2.01 AMS text
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks21
\ex@=\dimen174
)) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen175
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count286
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count287
\leftroot@=\count288
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count289
\DOTSCASE@=\count290
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box55
\strutbox@=\box56
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen176
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count291
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count292
\dotsspace@=\muskip17
\c@parentequation=\count293
\dspbrk@lvl=\count294
\tag@help=\toks22
\row@=\count295
\column@=\count296
\maxfields@=\count297
\andhelp@=\toks23
\eqnshift@=\dimen177
\alignsep@=\dimen178
\tagshift@=\dimen179
\tagwidth@=\dimen180
\totwidth@=\dimen181
\lineht@=\dimen182
\@envbody=\toks24
\multlinegap=\skip66
\multlinetaggap=\skip67
\mathdisplay@stack=\toks25
LaTeX Info: Redefining \[ on input line 2949.
LaTeX Info: Redefining \] on input line 2950.
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks26
\thm@bodyfont=\toks27
\thm@headfont=\toks28
\thm@notefont=\toks29
\thm@headpunct=\toks30
\thm@preskip=\skip68
\thm@postskip=\skip69
\thm@headsep=\skip70
\dth@everypar=\toks31
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/tcolorbox/tcolorbox.sty
Package: tcolorbox 2025/07/08 version 6.7.1 text color boxes
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks32
\pgfutil@tempdima=\dimen183
\pgfutil@tempdimb=\dimen184
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box57
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks33
\pgfkeys@temptoks=\toks34
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks35
))
\pgf@x=\dimen185
\pgf@y=\dimen186
\pgf@xa=\dimen187
\pgf@ya=\dimen188
\pgf@xb=\dimen189
\pgf@yb=\dimen190
\pgf@xc=\dimen191
\pgf@yc=\dimen192
\pgf@xd=\dimen193
\pgf@yd=\dimen194
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count298
\c@pgf@countb=\count299
\c@pgf@countc=\count300
\c@pgf@countd=\count301
\t@pgf@toka=\toks36
\t@pgf@tokb=\toks37
\t@pgf@tokc=\toks38
\pgf@sys@id@count=\count302
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-xetex.def
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-xetex.def
File: pgfsys-xetex.def 2023-01-15 v3.1.10 (3.1.10)
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-dvipdfmx.def
File: pgfsys-dvipdfmx.def 2023-01-15 v3.1.10 (3.1.10)
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)
\pgfsys@objnum=\count303
))) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count304
\pgfsyssoftpath@bigbuffer@items=\count305
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen195
\pgfmath@count=\count306
\pgfmath@box=\box58
\pgfmath@toks=\toks39
\pgfmath@stack@operand=\toks40
\pgfmath@stack@operation=\toks41
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count307
)) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/math/pgfint.code.tex) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen196
\pgf@picmaxx=\dimen197
\pgf@picminy=\dimen198
\pgf@picmaxy=\dimen199
\pgf@pathminx=\dimen256
\pgf@pathmaxx=\dimen257
\pgf@pathminy=\dimen258
\pgf@pathmaxy=\dimen259
\pgf@xx=\dimen260
\pgf@xy=\dimen261
\pgf@yx=\dimen262
\pgf@yy=\dimen263
\pgf@zx=\dimen264
\pgf@zy=\dimen265
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen266
\pgf@path@lasty=\dimen267
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen268
\pgf@shorten@start@additional=\dimen269
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box59
\pgf@hbox=\box60
\pgf@layerbox@main=\box61
\pgf@picture@serial@count=\count308
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen270
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen271
\pgf@pt@y=\dimen272
\pgf@pt@temp=\dimen273
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen274
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen275
\pgf@sys@shading@range@num=\count309
\pgf@shadingcount=\count310
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box62
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box63
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen276
\pgf@nodesepend=\dimen277
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
)) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/pgf/utilities/pgffor.sty (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex)) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/pgf/math/pgfmath.sty (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen278
\pgffor@skip=\dimen279
\pgffor@stack=\toks42
\pgffor@toks=\toks43
)) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count311
\pgfplotmarksize=\dimen280
)
\tikz@lastx=\dimen281
\tikz@lasty=\dimen282
\tikz@lastxsaved=\dimen283
\tikz@lastysaved=\dimen284
\tikz@lastmovetox=\dimen285
\tikz@lastmovetoy=\dimen286
\tikzleveldistance=\dimen287
\tikzsiblingdistance=\dimen288
\tikz@figbox=\box64
\tikz@figbox@bg=\box65
\tikz@tempbox=\box66
\tikz@tempbox@bg=\box67
\tikztreelevel=\count312
\tikznumberofchildren=\count313
\tikznumberofcurrentchild=\count314
\tikz@fig@count=\count315
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count316
\pgfmatrixcurrentcolumn=\count317
\pgf@matrix@numberofcolumns=\count318
)
\tikz@expandcount=\count319
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/tools/verbatim.sty
Package: verbatim 2024-01-22 v1.5x LaTeX2e package for verbatim enhancements
\every@verbatim=\toks44
\verbatim@line=\toks45
\verbatim@in@stream=\read3
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/environ/environ.sty
Package: environ 2014/05/04 v0.3 A new way to define environments
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/trimspaces/trimspaces.sty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
)) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count320
)
\tcb@titlebox=\box68
\tcb@upperbox=\box69
\tcb@lowerbox=\box70
\tcb@phantombox=\box71
\c@tcbbreakpart=\count321
\c@tcblayer=\count322
\c@tcolorbox@number=\count323
\l__tcobox_tmpa_box=\box72
\l__tcobox_tmpa_dim=\dimen289
\tcb@temp=\box73
\tcb@temp=\box74
\tcb@temp=\box75
\tcb@temp=\box76
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/tools/multicol.sty
Package: multicol 2025/05/25 v2.0a multicolumn formatting (FMi)
\c@tracingmulticols=\count324
\mult@box=\box77
\multicol@leftmargin=\dimen290
\c@unbalance=\count325
\c@collectmore=\count326
\doublecol@number=\count327
\multicoltolerance=\count328
\multicolpretolerance=\count329
\full@width=\dimen291
\page@free=\dimen292
\premulticols=\dimen293
\postmulticols=\dimen294
\multicolsep=\skip71
\multicolbaselineskip=\skip72
\partial@page=\box78
\last@line=\box79
\mc@boxedresult=\box80
\maxbalancingoverflow=\dimen295
\mult@rightbox=\box81
\mult@grightbox=\box82
\mult@firstbox=\box83
\mult@gfirstbox=\box84
\@tempa=\box85
\@tempa=\box86
\@tempa=\box87
\@tempa=\box88
\@tempa=\box89
\@tempa=\box90
\@tempa=\box91
\@tempa=\box92
\@tempa=\box93
\@tempa=\box94
\@tempa=\box95
\@tempa=\box96
\@tempa=\box97
\@tempa=\box98
\@tempa=\box99
\@tempa=\box100
\@tempa=\box101
\@tempa=\box102
\@tempa=\box103
\@tempa=\box104
\@tempa=\box105
\@tempa=\box106
\@tempa=\box107
\@tempa=\box108
\@tempa=\box109
\@tempa=\box110
\@tempa=\box111
\@tempa=\box112
\@tempa=\box113
\@tempa=\box114
\@tempa=\box115
\@tempa=\box116
\@tempa=\box117
\@tempa=\box118
\@tempa=\box119
\@tempa=\box120
\c@minrows=\count330
\c@columnbadness=\count331
\c@finalcolumnbadness=\count332
\last@try=\dimen296
\multicolovershoot=\dimen297
\multicolundershoot=\dimen298
\mult@nat@firstbox=\box121
\colbreak@box=\box122
\mc@col@check@num=\count333
\g__mc_curr_col_int=\count334
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\labelindent=\skip73
\enit@outerparindent=\dimen299
\enit@toks=\toks46
\enit@inbox=\box123
\enit@count@id=\count335
\enitdp@description=\count336
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/ragged2e/ragged2e.sty
Package: ragged2e 2023/06/22 v3.6 ragged2e Package
\CenteringLeftskip=\skip74
\RaggedLeftLeftskip=\skip75
\RaggedRightLeftskip=\skip76
\CenteringRightskip=\skip77
\RaggedLeftRightskip=\skip78
\RaggedRightRightskip=\skip79
\CenteringParfillskip=\skip80
\RaggedLeftParfillskip=\skip81
\RaggedRightParfillskip=\skip82
\JustifyingParfillskip=\skip83
\CenteringParindent=\skip84
\RaggedLeftParindent=\skip85
\RaggedRightParindent=\skip86
\JustifyingParindent=\skip87
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/titlesec/titlesec.sty
Package: titlesec 2025/01/04 v2.17 Sectioning titles
\ttl@box=\box124
\beforetitleunit=\skip88
\aftertitleunit=\skip89
\ttl@plus=\dimen300
\ttl@minus=\dimen301
\ttl@toksa=\toks47
\titlewidth=\dimen302
\titlewidthlast=\dimen303
\titlewidthfirst=\dimen304
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/fontspec/fontspec.sty (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/l3packages/xparse/xparse.sty (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-07-19 L3 programming layer (loader) 
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2025-06-09 L3 backend support: XeTeX
\g__graphics_track_int=\count337
\g__pdfannot_backend_int=\count338
\g__pdfannot_backend_link_int=\count339
))
Package: xparse 2024-08-16 L3 Experimental document command parser
)
Package: fontspec 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count340
\l__fontspec_language_int=\count341
\l__fontspec_strnum_int=\count342
\l__fontspec_tmp_int=\count343
\l__fontspec_tmpa_int=\count344
\l__fontspec_tmpb_int=\count345
\l__fontspec_tmpc_int=\count346
\l__fontspec_em_int=\count347
\l__fontspec_emdef_int=\count348
\l__fontspec_strong_int=\count349
\l__fontspec_strongdef_int=\count350
\l__fontspec_tmpa_dim=\dimen305
\l__fontspec_tmpb_dim=\dimen306
\l__fontspec_tmpc_dim=\dimen307
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2024/12/21 v2.1c Standard LaTeX package
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/fontspec/fontspec.cfg))) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/parskip/parskip.sty
Package: parskip 2021-03-14 v2.0h non-zero parskip adjustments
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
))) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/natbib/natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip90
\bibsep=\skip91
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count351
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/tools/indentfirst.sty
Package: indentfirst 2023/07/02 v1.03 Indent first paragraph (DPC)
)
(c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/placeins/placeins.sty
Package: placeins 2005/04/18  v 2.2
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/makecell/makecell.sty
Package: makecell 2009/08/03 V0.1e Managing of Tab Column Heads and Cells
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/tools/array.sty
Package: array 2025/06/08 v2.6j Tabular extension package (FMi)
\col@sep=\dimen308
\ar@mcellbox=\box125
\extrarowheight=\dimen309
\NC@list=\toks48
\extratabsurround=\skip92
\backup@length=\skip93
\ar@cellbox=\box126
)
\rotheadsize=\dimen310
\c@nlinenum=\count352
\TeXr@lab=\toks49
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen311
\lightrulewidth=\dimen312
\cmidrulewidth=\dimen313
\belowrulesep=\dimen314
\belowbottomsep=\dimen315
\aboverulesep=\dimen316
\abovetopsep=\dimen317
\cmidrulesep=\dimen318
\cmidrulekern=\dimen319
\defaultaddspace=\dimen320
\@cmidla=\count353
\@cmidlb=\count354
\@aboverulesep=\dimen321
\@belowrulesep=\dimen322
\@thisruleclass=\count355
\@lastruleclass=\count356
\@thisrulewidth=\dimen323
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2025-07-12 v7.01o Hypertext links for LaTeX
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2025-06-21 v2.57 Cross-referencing by name of section
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count357
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\@linkdim=\dimen324
\Hy@linkcounter=\count358
\Hy@pagecounter=\count359
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2025-07-12 v7.01o Hyperref: PDFDocEncoding definition (HO)
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count360
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2025-07-12 v7.01o Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `colorlinks' set `true' on input line 4066.
Package hyperref Info: Hyper figures OFF on input line 4195.
Package hyperref Info: Link nesting OFF on input line 4200.
Package hyperref Info: Hyper index ON on input line 4203.
Package hyperref Info: Plain pages OFF on input line 4210.
Package hyperref Info: Backreferencing OFF on input line 4215.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4462.
\c@Hy@tempcnt=\count361
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4801.
\XeTeXLinkMargin=\dimen325
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count362
\Field@Width=\dimen326
\Fld@charsize=\dimen327
Package hyperref Info: Hyper figures OFF on input line 6078.
Package hyperref Info: Link nesting OFF on input line 6083.
Package hyperref Info: Hyper index ON on input line 6086.
Package hyperref Info: backreferencing OFF on input line 6093.
Package hyperref Info: Link coloring ON on input line 6096.
Package hyperref Info: Link coloring with OCG OFF on input line 6103.
Package hyperref Info: PDF/A mode OFF on input line 6108.
\Hy@abspage=\count363
\c@Item=\count364
\c@Hfootnote=\count365
)
Package hyperref Info: Driver (autodetected): hxetex.
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/hyperref/hxetex.def
File: hxetex.def 2025-07-12 v7.01o Hyperref driver for XeTeX
\pdfm@box=\box127
\c@Hy@AnnotLevel=\count366
\HyField@AnnotCount=\count367
\Fld@listcount=\count368
\c@bookmark@seq@number=\count369
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2025-06-21 v1.11 Rerun checks for auxiliary files (HO)
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 284.
)
\Hy@SectionHShift=\skip94
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/tools/tabularx.sty
Package: tabularx 2023/12/11 v2.12a `tabularx' package (DPC)
\TX@col@width=\dimen328
\TX@old@table=\dimen329
\TX@old@col=\dimen330
\TX@target=\dimen331
\TX@delta=\dimen332
\TX@cols=\count370
\TX@ftn=\toks50
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip95
\multirow@cntb=\count371
\multirow@dima=\skip96
\bigstrutjot=\dimen333
) (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count372
\float@exts=\toks51
\float@box=\box128
\@float@everytoks=\toks52
\@floatcapt=\box129
)
\c@theorem=\count373
\c@lemma=\count374
\c@corollary=\count375
\c@proposition=\count376
\c@characterization=\count377
\c@property=\count378
\c@problem=\count379
\c@example=\count380
\c@examplesanddefinitions=\count381
\c@remark=\count382
\c@definition=\count383
\c@hypothesis=\count384
\c@notation=\count385
\c@assumption=\count386
\c@algorithm=\count387

Package fontspec Info: 
(fontspec)             Font family 'TimesNewRoman(0)' created for font 'Times
(fontspec)             New Roman' with options [Ligatures=TeX].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.: <->"Times New
(fontspec)             Roman/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: <->"Times New
(fontspec)             Roman/OT:script=latn;language=dflt;+smcp;mapping=tex-text;"
(fontspec)             - 'bold' (b/n) with NFSS spec.: <->"Times New
(fontspec)             Roman/B/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: <->"Times
(fontspec)             New
(fontspec)             Roman/B/OT:script=latn;language=dflt;+smcp;mapping=tex-text;"
(fontspec)             - 'italic' (m/it) with NFSS spec.: <->"Times New
(fontspec)             Roman/I/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.:
(fontspec)             <->"Times New
(fontspec)             Roman/I/OT:script=latn;language=dflt;+smcp;mapping=tex-text;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.: <->"Times New
(fontspec)             Roman/BI/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'bold italic small caps'  (b/scit) with NFSS spec.:
(fontspec)             <->"Times New
(fontspec)             Roman/BI/OT:script=latn;language=dflt;+smcp;mapping=tex-text;"

 (./output.aux)
\openout1 = `output.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 287.
LaTeX Font Info:    ... okay on input line 287.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 287.
LaTeX Font Info:    ... okay on input line 287.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 287.
LaTeX Font Info:    ... okay on input line 287.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 287.
LaTeX Font Info:    ... okay on input line 287.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 287.
LaTeX Font Info:    ... okay on input line 287.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 287.
LaTeX Font Info:    ... okay on input line 287.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 287.
LaTeX Font Info:    ... okay on input line 287.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 287.
LaTeX Font Info:    ... okay on input line 287.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 287.
LaTeX Font Info:    ... okay on input line 287.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 287.
LaTeX Font Info:    ... okay on input line 287.

*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: custom
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(56.9055pt, 483.69687pt, 56.9055pt)
* v-part:(T,H,B)=(71.13188pt, 668.63977pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=810.90353pt
* \textwidth=483.69687pt
* \textheight=668.63977pt
* \oddsidemargin=-15.36449pt
* \evensidemargin=-15.36449pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=59.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package caption Info: Begin \AtBeginDocument code.
Package caption Info: float package is loaded.
Package caption Info: hyperref package is loaded.
Package caption Info: End \AtBeginDocument code.

Package fontspec Info: 
(fontspec)             Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 287.
LaTeX Font Info:    Redeclaring math accent \acute on input line 287.
LaTeX Font Info:    Redeclaring math accent \grave on input line 287.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 287.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 287.
LaTeX Font Info:    Redeclaring math accent \bar on input line 287.
LaTeX Font Info:    Redeclaring math accent \breve on input line 287.
LaTeX Font Info:    Redeclaring math accent \check on input line 287.
LaTeX Font Info:    Redeclaring math accent \hat on input line 287.
LaTeX Font Info:    Redeclaring math accent \dot on input line 287.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 287.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 287.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 287.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 287.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 287.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 287.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 287.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 287.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 287.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 287.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 287.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 287.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 287.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 287.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 287.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/TimesNewRoman(0)/m/n on input line 287.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 287.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/TimesNewRoman(0)/m/n on input line 287.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/TimesNewRoman(0)/m/n --> TU/TimesNewRoman(0)/m/n on input line 287.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/TimesNewRoman(0)/m/it on input line 287.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/TimesNewRoman(0)/b/n on input line 287.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 287.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 287.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/TimesNewRoman(0)/m/n --> TU/TimesNewRoman(0)/b/n on input line 287.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/TimesNewRoman(0)/b/it on input line 287.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/b/n on input line 287.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 287.
Package hyperref Info: Link coloring ON on input line 287.
(./output.out) (./output.out)
\@outlinefile=\write4
\openout4 = `output.out'.

File: logo.png Graphic file (type bmp)
<logo.png>
LaTeX Font Info:    Trying to load font information for U+msa on input line 316.
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 316.
 (c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
Overfull \hbox (1.65002pt too wide) in paragraph at lines 310--330
$[]$    $[]$
 []

File: ORCID.png Graphic file (type bmp)
<ORCID.png>
File: ORCID.png Graphic file (type bmp)
<ORCID.png>
File: ORCID.png Graphic file (type bmp)
<ORCID.png>



Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 20.63005pt, for example:
(fancyhdr)                \setlength{\headheight}{20.63005pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-8.63005pt}.

[1071

]






Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 20.63005pt, for example:
(fancyhdr)                \setlength{\headheight}{20.63005pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-8.63005pt}.

[1072]






Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 20.63005pt, for example:
(fancyhdr)                \setlength{\headheight}{20.63005pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-8.63005pt}.

[1073]





File: images/figure1.png Graphic file (type bmp)
<images/figure1.png>







Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 20.63005pt, for example:
(fancyhdr)                \setlength{\headheight}{20.63005pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-8.63005pt}.

[1074]
Overfull \vbox (0.36023pt too high) has occurred while \output is active []










Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 20.63005pt, for example:
(fancyhdr)                \setlength{\headheight}{20.63005pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-8.63005pt}.

[1075]
Overfull \hbox (24.0pt too wide) in paragraph at lines 479--496
[][] 
 []








Overfull \hbox (12.0pt too wide) in paragraph at lines 540--556
[][] 
 []








Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 20.63005pt, for example:
(fancyhdr)                \setlength{\headheight}{20.63005pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-8.63005pt}.

[1076]






Overfull \hbox (12.0pt too wide) in paragraph at lines 593--609
[][] 
 []








Underfull \hbox (badness 10000) in paragraph at lines 658--658
[]|\TU/TimesNewRoman(0)/b/n/10 Evaluation
 []


Overfull \hbox (16.46901pt too wide) in paragraph at lines 656--683
[][] 
 []




Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 20.63005pt, for example:
(fancyhdr)                \setlength{\headheight}{20.63005pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-8.63005pt}.

[1077]






Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 20.63005pt, for example:
(fancyhdr)                \setlength{\headheight}{20.63005pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-8.63005pt}.

[1078]






Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 20.63005pt, for example:
(fancyhdr)                \setlength{\headheight}{20.63005pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-8.63005pt}.

[1079]






Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 20.63005pt, for example:
(fancyhdr)                \setlength{\headheight}{20.63005pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-8.63005pt}.

[1080]






Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 20.63005pt, for example:
(fancyhdr)                \setlength{\headheight}{20.63005pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-8.63005pt}.

[1081]
Underfull \hbox (badness 7116) in paragraph at lines 810--811
[]\TU/TimesNewRoman(0)/m/n/10 Walker, K., 2020,  Purposive sampling: com-
 []


Underfull \hbox (badness 10000) in paragraph at lines 810--811
\TU/TimesNewRoman(0)/m/n/10 plex or simple? Research case examples, Jour-
 []








Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 20.63005pt, for example:
(fancyhdr)                \setlength{\headheight}{20.63005pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-8.63005pt}.

[1082]
Underfull \hbox (badness 6188) in paragraph at lines 878--878
|\TU/TimesNewRoman(0)/b/n/15 Institutional Review Board State-
 []

File: images/figure2.png Graphic file (type bmp)
<images/figure2.png>

Overfull \hbox (5.0pt too wide) in paragraph at lines 900--903
 [][] 
 []







File: images/figure3.png Graphic file (type bmp)
<images/figure3.png>



Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 20.63005pt, for example:
(fancyhdr)                \setlength{\headheight}{20.63005pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-8.63005pt}.

[1083]
File: images/figure4.png Graphic file (type bmp)
<images/figure4.png>

! Misplaced alignment tab character &.
l.943 \bibitem{8} \label{ref8} Hutchinson, T., &
                                                 Waters, A. (1987). \textit{...
I can't figure out why you would want to use a tab mark
here. If you just want an ampersand, the remedy is
simple: Just type `I\&' now. But if some right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

! Misplaced alignment tab character &.
l.944 \bibitem{9} \label{ref9} Dudley-Evans, T., &
                                                   St John, M. J. (1998). \t...
I can't figure out why you would want to use a tab mark
here. If you just want an ampersand, the remedy is
simple: Just type `I\&' now. But if some right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

! Misplaced alignment tab character &.
l.950 \bibitem{15} \label{ref15} Flowerdew, J., &
                                                  Peacock, M. (2001). \texti...
I can't figure out why you would want to use a tab mark
here. If you just want an ampersand, the remedy is
simple: Just type `I\&' now. But if some right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

! Misplaced alignment tab character &.
l.955 ...aihom, A., Chutopama, N., Putklang, T., &
                                                   Tanuanram, T. (2025). Inn...
I can't figure out why you would want to use a tab mark
here. If you just want an ampersand, the remedy is
simple: Just type `I\&' now. But if some right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.








Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 20.63005pt, for example:
(fancyhdr)                \setlength{\headheight}{20.63005pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-8.63005pt}.

[1084]
! Misplaced alignment tab character &.
l.962 \bibitem{27} \label{ref27} Kathirvel, K., &
                                                  Hashim, H. (2020). The use...
I can't figure out why you would want to use a tab mark
here. If you just want an ampersand, the remedy is
simple: Just type `I\&' now. But if some right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

! Misplaced alignment tab character &.
l.963 ...l{ref28} Asratie, M. G., Melese, E. A., &
                                                   Ayalew, D. A. (2021). The...
I can't figure out why you would want to use a tab mark
here. If you just want an ampersand, the remedy is
simple: Just type `I\&' now. But if some right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

! Misplaced alignment tab character &.
l.964 ...em{29} \label{ref29} Pratama, A. D. Y., &
                                                   Suastha, P. V. M. (2020)....
I can't figure out why you would want to use a tab mark
here. If you just want an ampersand, the remedy is
simple: Just type `I\&' now. But if some right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.










Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 20.63005pt, for example:
(fancyhdr)                \setlength{\headheight}{20.63005pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-8.63005pt}.

[1085] (./output.aux)
 ***********
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-07-19>
 ***********
Package rerunfilecheck Info: File `output.out' has not changed.
(rerunfilecheck)             Checksum: 65921CD9F5CB5033F34F51B3F09D3383;1291.
 ) 
Here is how much of TeX's memory you used:
 28730 strings out of 470473
 594510 string characters out of 5489164
 1034311 words of memory out of 5000000
 56585 multiletter control sequences out of 15000+600000
 633788 words of font info for 99 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 98i,12n,101p,4058b,701s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on output.pdf (15 pages).

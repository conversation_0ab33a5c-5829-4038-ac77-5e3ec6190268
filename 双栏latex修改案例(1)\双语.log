This is LuaHBTeX, Version 1.18.0 (TeX Live 2024)  (format=lualatex 2024.9.12)  22 JUL 2025 15:59
 restricted system commands enabled.
**双语.tex
(./双语.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
Lua module: luaotfload 2024-02-14 3.28 Lua based OpenType font support
Lua module: lualibs 2023-07-13 2.76 ConTeXt Lua standard libraries.
Lua module: lualibs-extended 2023-07-13 2.76 ConTeXt Lua libraries -- extended c
ollection.
luaotfload | conf : Root cache directory is "C:/texlive/2024/texmf-var/luatex-ca
che/generic/names".
luaotfload | init : Loading fontloader "fontloader-2023-12-28.lua" from kpse-res
olved path "c:/texlive/2024/texmf-dist/tex/luatex/luaotfload/fontloader-2023-12-
28.lua".
Lua-only attribute luaotfload@noligature = 1
luaotfload | init : Context OpenType loader version 3.134
Inserting `luaotfload.node_processor' in `pre_linebreak_filter'.
Inserting `luaotfload.node_processor' in `hpack_filter'.
Inserting `luaotfload.glyph_stream' in `glyph_stream_provider'.
Inserting `luaotfload.define_font' in `define_font'.
Lua-only attribute luaotfload_color_attribute = 2
luaotfload | conf : Root cache directory is "C:/texlive/2024/texmf-var/luatex-ca
che/generic/names".
Inserting `luaotfload.harf.strip_prefix' in `find_opentype_file'.
Inserting `luaotfload.harf.strip_prefix' in `find_truetype_file'.
Removing  `luaotfload.glyph_stream' from `glyph_stream_provider'.
Inserting `luaotfload.harf.glyphstream' in `glyph_stream_provider'.
Inserting `luaotfload.harf.finalize_vlist' in `post_linebreak_filter'.
Inserting `luaotfload.harf.finalize_hlist' in `hpack_filter'.
Inserting `luaotfload.cleanup_files' in `wrapup_run'.
Inserting `luaotfload.harf.finalize_unicode' in `finish_pdffile'.
Inserting `luaotfload.glyphinfo' in `glyph_info'.
Lua-only attribute luaotfload.letterspace_done = 3
Inserting `luaotfload.aux.set_sscale_dimens' in `luaotfload.patch_font'.
Inserting `luaotfload.aux.set_font_index' in `luaotfload.patch_font'.
Inserting `luaotfload.aux.patch_cambria_domh' in `luaotfload.patch_font'.
Inserting `luaotfload.aux.fixup_fontdata' in `luaotfload.patch_font_unsafe'.
Inserting `luaotfload.aux.set_capheight' in `luaotfload.patch_font'.
Inserting `luaotfload.aux.set_xheight' in `luaotfload.patch_font'.
Inserting `luaotfload.rewrite_fontname' in `luaotfload.patch_font'.
Inserting `tracingstacklevels' in `input_level_string'. (c:/texlive/2024/texmf-
dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(c:/texlive/2024/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
luaotfload | db : Font names database loaded from C:/texlive/2024/texmf-var/luat
ex-cache/generic/names/luaotfload-names.luc.gz)
\c@part=\count186
\c@section=\count187
\c@subsection=\count188
\c@subsubsection=\count189
\c@paragraph=\count190
\c@subparagraph=\count191
\c@figure=\count192
\c@table=\count193
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen139
)
(c:/texlive/2024/texmf-dist/tex/latex/sttools/flushend.sty
Package: flushend 2021/10/04 v4.0 Balancing columns in twocolumn mode
\flushend@@lastskip@a=\skip50
\flushend@@lastskip@b=\skip51
\flushend@@lastnode=\count194
\var@@loop@iter=\count195
\var@@temp@spread=\dimen140
\var@@temp@a=\dimen141
\var@@temp@loop=\dimen142
\flushend@@page@rule=\dimen143
\flushend@@varbox@lastpage=\box51
\flushend@@varbox@a=\box52
\flushend@@varbox@c=\box53
\flushend@@tempbox@a=\box54
\flushend@@tempbox@c=\box55
\flushend@@floatbox=\box56
\@viper=\box57
\hold@viper=\box58
\atColsBreak=\toks17
\atColsEnd=\toks18
\oldbreak@skip=\dimen144
)
(c:/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(c:/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks19
)
(c:/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)

(c:/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
)
(c:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: luatex.def on input line 107.

(c:/texlive/2024/texmf-dist/tex/latex/graphics-def/luatex.def
File: luatex.def 2022/09/22 v1.2d Graphics/color driver for luatex
))
\Gin@req@height=\dimen145
\Gin@req@width=\dimen146
)
(c:/texlive/2024/texmf-dist/tex/latex/preprint/balance.sty
Package: balance 1999/02/23 4.3 (PWD)
\oldvsize=\dimen147
)
(c:/texlive/2024/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(c:/texlive/2024/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(c:/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count196
\Gm@cntv=\count197
\c@Gm@tempcnt=\count198
\Gm@bindingoffset=\dimen148
\Gm@wd@mp=\dimen149
\Gm@odd@mp=\dimen150
\Gm@even@mp=\dimen151
\Gm@layoutwidth=\dimen152
\Gm@layoutheight=\dimen153
\Gm@layouthoffset=\dimen154
\Gm@layoutvoffset=\dimen155
\Gm@dimlist=\toks20
)
(c:/texlive/2024/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2022/11/09 v4.1 Extensive control of page headers and footers

\f@nch@headwidth=\skip52
\f@nch@O@elh=\skip53
\f@nch@O@erh=\skip54
\f@nch@O@olh=\skip55
\f@nch@O@orh=\skip56
\f@nch@O@elf=\skip57
\f@nch@O@erf=\skip58
\f@nch@O@olf=\skip59
\f@nch@O@orf=\skip60
)
(c:/texlive/2024/texmf-dist/tex/latex/textpos/textpos.sty
Package: textpos 2022/07/23 v1.10.1
Package textpos Info: choosing support for LaTeX3 on input line 60.
\TP@textbox=\box59
\TP@holdbox=\box60
\TPHorizModule=\dimen156
\TPVertModule=\dimen157
\TP@margin=\dimen158
\TP@absmargin=\dimen159

Grid set 16 x 16 = 37.34424pt x 50.68146pt
\TPboxrulesize=\dimen160
\TP@ox=\dimen161
\TP@oy=\dimen162
\TP@tbargs=\toks21
TextBlockOrigin set to 0pt x 0pt
) (c:/texlive/2024/texmf-dist/tex/latex/caption/subcaption.sty
Package: subcaption 2023/07/28 v1.6b Sub-captions (AR)

(c:/texlive/2024/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)

(c:/texlive/2024/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen163
\captionmargin=\dimen164
\caption@leftmargin=\dimen165
\caption@rightmargin=\dimen166
\caption@width=\dimen167
\caption@indent=\dimen168
\caption@parindent=\dimen169
\caption@hangindent=\dimen170
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count199
\c@continuedfloat=\count266
)
Package caption Info: New subtype `subfigure' on input line 238.
\c@subfigure=\count267
Package caption Info: New subtype `subtable' on input line 238.
\c@subtable=\count268
)
(c:/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip61

For additional information on amsmath, use the `?' option.
(c:/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(c:/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks22
\ex@=\dimen171
))
(c:/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen172
)
(c:/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count269
LaTeX Info: Redefining \frac on input line 236.
\uproot@=\count270
\leftroot@=\count271
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count272
\DOTSCASE@=\count273
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box61
\strutbox@=\box62
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen173
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count274
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count275
\dotsspace@=\muskip16
\c@parentequation=\count276
\dspbrk@lvl=\count277
\tag@help=\toks23
\row@=\count278
\column@=\count279
\maxfields@=\count280
\andhelp@=\toks24
\eqnshift@=\dimen174
\alignsep@=\dimen175
\tagshift@=\dimen176
\tagwidth@=\dimen177
\totwidth@=\dimen178
\lineht@=\dimen179
\@envbody=\toks25
\multlinegap=\skip62
\multlinetaggap=\skip63
\mathdisplay@stack=\toks26
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(c:/texlive/2024/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(c:/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(c:/texlive/2024/texmf-dist/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks27
\thm@bodyfont=\toks28
\thm@headfont=\toks29
\thm@notefont=\toks30
\thm@headpunct=\toks31
\thm@preskip=\skip64
\thm@postskip=\skip65
\thm@headsep=\skip66
\dth@everypar=\toks32
)
(c:/texlive/2024/texmf-dist/tex/latex/tools/bm.sty
Package: bm 2023/07/08 v1.2f Bold Symbol Support (DPC/FMi)
\symboldoperators=\mathgroup6
\symboldletters=\mathgroup7
\symboldsymbols=\mathgroup8
Package bm Info: No bold for \OMX/cmex/m/n, using \pmb.
Package bm Info: No bold for \U/msa/m/n, using \pmb.
Package bm Info: No bold for \U/msb/m/n, using \pmb.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 149.
)
(c:/texlive/2024/texmf-dist/tex/latex/changepage/changepage.sty
Package: changepage 2009/10/20 v1.0c check page and change page layout
\c@cp@cntr=\count281
\cp@tempcnt=\count282
)
(c:/texlive/2024/texmf-dist/tex/latex/lineno/lineno.sty
Package: lineno 2023/05/20 line numbers on paragraphs v5.3

(c:/texlive/2024/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count283
)
(c:/texlive/2024/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)

(c:/texlive/2024/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(c:/texlive/2024/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
))
\linenopenalty=\count284
\output=\toks33
\linenoprevgraf=\count285
\linenumbersep=\dimen180
\linenumberwidth=\dimen181
\c@linenumber=\count286
\c@pagewiselinenumber=\count287
\c@LN@truepage=\count288
\c@internallinenumber=\count289
\c@internallinenumbers=\count290
\quotelinenumbersep=\dimen182
\bframerule=\dimen183
\bframesep=\dimen184
\bframebox=\box63
\linenoamsmath@ams@eqpen=\count291
LaTeX Info: Redefining \\ on input line 3180.
)
(c:/texlive/2024/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)

(c:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: luatex.def on input line 274.

(c:/texlive/2024/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
)
(c:/texlive/2024/texmf-dist/tex/latex/tcolorbox/tcolorbox.sty
Package: tcolorbox 2024/01/10 version 6.2.0 text color boxes

(c:/texlive/2024/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
(c:/texlive/2024/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty
(c:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks34
\pgfutil@tempdima=\dimen185
\pgfutil@tempdimb=\dimen186
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box64
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex
(c:/texlive/2024/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(c:/texlive/2024/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty
(c:/texlive/2024/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty
(c:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)

(c:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks35
\pgfkeys@temptoks=\toks36

(c:/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.co
de.tex
\pgfkeys@tmptoks=\toks37
))
\pgf@x=\dimen187
\pgf@y=\dimen188
\pgf@xa=\dimen189
\pgf@ya=\dimen190
\pgf@xb=\dimen191
\pgf@yb=\dimen192
\pgf@xc=\dimen193
\pgf@yc=\dimen194
\pgf@xd=\dimen195
\pgf@yd=\dimen196
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count292
\c@pgf@countb=\count293
\c@pgf@countc=\count294
\c@pgf@countd=\count295
\t@pgf@toka=\toks38
\t@pgf@tokb=\toks39
\t@pgf@tokc=\toks40
\pgf@sys@id@count=\count296
 (c:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-luatex.def

(c:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-luatex.def
File: pgfsys-luatex.def 2023-01-15 v3.1.10 (3.1.10)

(c:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)))
(c:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count297
\pgfsyssoftpath@bigbuffer@items=\count298
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen197
\pgfmath@count=\count299
\pgfmath@box=\box65
\pgfmath@toks=\toks41
\pgfmath@stack@operand=\toks42
\pgfmath@stack@operation=\toks43
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.te
x)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric
.code.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.t
ex)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.co
de.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.te
x)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithm
etics.code.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count300
))
(c:/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfint.code.tex)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen198
\pgf@picmaxx=\dimen199
\pgf@picminy=\dimen256
\pgf@picmaxy=\dimen257
\pgf@pathminx=\dimen258
\pgf@pathmaxx=\dimen259
\pgf@pathminy=\dimen260
\pgf@pathmaxy=\dimen261
\pgf@xx=\dimen262
\pgf@xy=\dimen263
\pgf@yx=\dimen264
\pgf@yy=\dimen265
\pgf@zx=\dimen266
\pgf@zy=\dimen267
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.cod
e.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen268
\pgf@path@lasty=\dimen269
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.te
x
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen270
\pgf@shorten@start@additional=\dimen271
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box66
\pgf@hbox=\box67
\pgf@layerbox@main=\box68
\pgf@picture@serial@count=\count301
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code
.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen272
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.c
ode.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen273
\pgf@pt@y=\dimen274
\pgf@pt@temp=\dimen275
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.co
de.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen276
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen277
\pgf@sys@shading@range@num=\count302
\pgf@shadingcount=\count303
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box69
) (c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code
.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (c:/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (c:/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.te
x
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box70
)
(c:/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(c:/texlive/2024/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.st
y
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen278
\pgf@nodesepend=\dimen279
)
(c:/texlive/2024/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.st
y
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
)) (c:/texlive/2024/texmf-dist/tex/latex/tools/verbatim.sty
Package: verbatim 2023-11-06 v1.5v LaTeX2e package for verbatim enhancements
\every@verbatim=\toks44
\verbatim@line=\toks45
\verbatim@in@stream=\read3
)
(c:/texlive/2024/texmf-dist/tex/latex/environ/environ.sty
Package: environ 2014/05/04 v0.3 A new way to define environments

(c:/texlive/2024/texmf-dist/tex/latex/trimspaces/trimspaces.sty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
))
\tcb@titlebox=\box71
\tcb@upperbox=\box72
\tcb@lowerbox=\box73
\tcb@phantombox=\box74
\c@tcbbreakpart=\count304
\c@tcblayer=\count305
\c@tcolorbox@number=\count306
\l__tcobox_tmpa_box=\box75
\l__tcobox_tmpa_dim=\dimen280
\tcb@temp=\box76
\tcb@temp=\box77
\tcb@temp=\box78
\tcb@temp=\box79
)
(c:/texlive/2024/texmf-dist/tex/latex/tools/multicol.sty
Package: multicol 2023/03/30 v1.9f multicolumn formatting (FMi)
\c@tracingmulticols=\count307
\mult@box=\box80
\multicol@leftmargin=\dimen281
\c@unbalance=\count308
\c@collectmore=\count309
\doublecol@number=\count310
\multicoltolerance=\count311
\multicolpretolerance=\count312
\full@width=\dimen282
\page@free=\dimen283
\premulticols=\dimen284
\postmulticols=\dimen285
\multicolsep=\skip67
\multicolbaselineskip=\skip68
\partial@page=\box81
\last@line=\box82
\mc@boxedresult=\box83
\maxbalancingoverflow=\dimen286
\mult@rightbox=\box84
\mult@grightbox=\box85
\mult@firstbox=\box86
\mult@gfirstbox=\box87
\@tempa=\box88
\@tempa=\box89
\@tempa=\box90
\@tempa=\box91
\@tempa=\box92
\@tempa=\box93
\@tempa=\box94
\@tempa=\box95
\@tempa=\box96
\@tempa=\box97
\@tempa=\box98
\@tempa=\box99
\@tempa=\box100
\@tempa=\box101
\@tempa=\box102
\@tempa=\box103
\@tempa=\box104
\@tempa=\box105
\@tempa=\box106
\@tempa=\box107
\@tempa=\box108
\@tempa=\box109
\@tempa=\box110
\@tempa=\box111
\@tempa=\box112
\@tempa=\box113
\@tempa=\box114
\@tempa=\box115
\@tempa=\box116
\@tempa=\box117
\@tempa=\box118
\@tempa=\box119
\@tempa=\box120
\@tempa=\box121
\@tempa=\box122
\@tempa=\box123
\c@minrows=\count313
\c@columnbadness=\count314
\c@finalcolumnbadness=\count315
\last@try=\dimen287
\multicolovershoot=\dimen288
\multicolundershoot=\dimen289
\mult@nat@firstbox=\box124
\colbreak@box=\box125
\mc@col@check@num=\count316
)
(c:/texlive/2024/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2019/06/20 v3.9 Customized lists
\labelindent=\skip69
\enit@outerparindent=\dimen290
\enit@toks=\toks46
\enit@inbox=\box126
\enit@count@id=\count317
\enitdp@description=\count318
)
(c:/texlive/2024/texmf-dist/tex/latex/ragged2e/ragged2e.sty
Package: ragged2e 2023/06/22 v3.6 ragged2e Package
\CenteringLeftskip=\skip70
\RaggedLeftLeftskip=\skip71
\RaggedRightLeftskip=\skip72
\CenteringRightskip=\skip73
\RaggedLeftRightskip=\skip74
\RaggedRightRightskip=\skip75
\CenteringParfillskip=\skip76
\RaggedLeftParfillskip=\skip77
\RaggedRightParfillskip=\skip78
\JustifyingParfillskip=\skip79
\CenteringParindent=\skip80
\RaggedLeftParindent=\skip81
\RaggedRightParindent=\skip82
\JustifyingParindent=\skip83
)
(c:/texlive/2024/texmf-dist/tex/latex/titlesec/titlesec.sty
Package: titlesec 2023/10/27 v2.16 Sectioning titles
\ttl@box=\box127
\beforetitleunit=\skip84
\aftertitleunit=\skip85
\ttl@plus=\dimen291
\ttl@minus=\dimen292
\ttl@toksa=\toks47
\titlewidth=\dimen293
\titlewidthlast=\dimen294
\titlewidthfirst=\dimen295
)
(c:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.sty
(c:/texlive/2024/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
(c:/texlive/2024/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-02-20 L3 programming layer (loader) 

(c:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-luatex.def
File: l3backend-luatex.def 2024-02-20 L3 backend support: PDF output (LuaTeX)
\l__color_backend_stack_int=\count319
Inserting `l3color' in `luaotfload.parse_color'.
\l__pdf_internal_box=\box128
))
Package: xparse 2024-02-18 L3 Experimental document command parser
)
Package: fontspec 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTeX
Lua module: fontspec 2024/02/13 2.9a Font selection for XeLaTeX and LuaLaTeX
(c:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec-luatex.sty
Package: fontspec-luatex 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaT
eX
\l__fontspec_script_int=\count320
\l__fontspec_language_int=\count321
\l__fontspec_strnum_int=\count322
\l__fontspec_tmp_int=\count323
\l__fontspec_tmpa_int=\count324
\l__fontspec_tmpb_int=\count325
\l__fontspec_tmpc_int=\count326
\l__fontspec_em_int=\count327
\l__fontspec_emdef_int=\count328
\l__fontspec_strong_int=\count329
\l__fontspec_strongdef_int=\count330
\l__fontspec_tmpa_dim=\dimen296
\l__fontspec_tmpb_dim=\dimen297
\l__fontspec_tmpc_dim=\dimen298

(c:/texlive/2024/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(c:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.cfg)))
(c:/texlive/2024/texmf-dist/tex/latex/parskip/parskip.sty
Package: parskip 2021-03-14 v2.0h non-zero parskip adjustments
)
(c:/texlive/2024/texmf-dist/tex/latex/titling/titling.sty
Package: titling 2009/09/04 v2.1d maketitle typesetting
\thanksmarkwidth=\skip86
\thanksmargin=\skip87
\droptitle=\skip88
)
(c:/texlive/2024/texmf-dist/tex/latex/lipsum/lipsum.sty
(c:/texlive/2024/texmf-dist/tex/latex/l3packages/l3keys2e/l3keys2e.sty
Package: l3keys2e 2024-02-18 LaTeX2e option processing using LaTeX3 keys
)
Package: lipsum 2021-09-20 v2.7 150 paragraphs of Lorem Ipsum dummy text
\g__lipsum_par_int=\count331
\l__lipsum_a_int=\count332
\l__lipsum_b_int=\count333

(c:/texlive/2024/texmf-dist/tex/latex/lipsum/lipsum.ltd.tex))
(c:/texlive/2024/texmf-dist/tex/latex/setspace/setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
)
(c:/texlive/2024/texmf-dist/tex/latex/natbib/natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip89
\bibsep=\skip90
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count334
)
(c:/texlive/2024/texmf-dist/tex/latex/tools/indentfirst.sty
Package: indentfirst 2023/07/02 v1.03 Indent first paragraph (DPC)
)
(c:/texlive/2024/texmf-dist/tex/latex/microtype/microtype.sty
Package: microtype 2023/03/13 v3.1a Micro-typographical refinements (RS)
\MT@toks=\toks48
\MT@tempbox=\box129
\MT@count=\count335
LaTeX Info: Redefining \noprotrusionifhmode on input line 1059.
LaTeX Info: Redefining \leftprotrusion on input line 1060.
\MT@prot@toks=\toks49
LaTeX Info: Redefining \rightprotrusion on input line 1078.
LaTeX Info: Redefining \textls on input line 1368.
\MT@outer@kern=\dimen299
LaTeX Info: Redefining \textmicrotypecontext on input line 1988.
\MT@listname@count=\count336

(c:/texlive/2024/texmf-dist/tex/latex/microtype/microtype-luatex.def
File: microtype-luatex.def 2023/03/13 v3.1a Definitions specific to luatex (RS)

Lua module: microtype 2023/03/13 3.1a microtype module.
Module microtype Info: overwriting function `keepligature' on input line 63
LaTeX Info: Redefining \lsstyle on input line 688.
LaTeX Info: Redefining \lslig on input line 688.
\MT@outer@space=\skip91
)
Package microtype Info: Loading configuration file microtype.cfg.

(c:/texlive/2024/texmf-dist/tex/latex/microtype/microtype.cfg
File: microtype.cfg 2023/03/13 v3.1a microtype main configuration file (RS)
))
(c:/texlive/2024/texmf-dist/tex/latex/titlesec/titletoc.sty
Package: titletoc 2023/10/27 v2.16 TOC entries
\ttl@leftsep=\dimen300
)
(c:/texlive/2024/texmf-dist/tex/latex/placeins/placeins.sty
Package: placeins 2005/04/18  v 2.2
)
(c:/texlive/2024/texmf-dist/tex/latex/makecell/makecell.sty
Package: makecell 2009/08/03 V0.1e Managing of Tab Column Heads and Cells

(c:/texlive/2024/texmf-dist/tex/latex/tools/array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen301
\ar@mcellbox=\box130
\extrarowheight=\dimen302
\NC@list=\toks50
\extratabsurround=\skip92
\backup@length=\skip93
\ar@cellbox=\box131
)
\rotheadsize=\dimen303
\c@nlinenum=\count337
\TeXr@lab=\toks51
)
(c:/texlive/2024/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen304
\lightrulewidth=\dimen305
\cmidrulewidth=\dimen306
\belowrulesep=\dimen307
\belowbottomsep=\dimen308
\aboverulesep=\dimen309
\abovetopsep=\dimen310
\cmidrulesep=\dimen311
\cmidrulekern=\dimen312
\defaultaddspace=\dimen313
\@cmidla=\count338
\@cmidlb=\count339
\@aboverulesep=\dimen314
\@belowrulesep=\dimen315
\@thisruleclass=\count340
\@lastruleclass=\count341
\@thisrulewidth=\dimen316
)
(c:/texlive/2024/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-01-20 v7.01h Hypertext links for LaTeX

(c:/texlive/2024/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(c:/texlive/2024/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(c:/texlive/2024/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(c:/texlive/2024/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
\pdftexcmds@toks=\toks52
))
(c:/texlive/2024/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(c:/texlive/2024/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
)
(c:/texlive/2024/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(c:/texlive/2024/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(c:/texlive/2024/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count342
)
\@linkdim=\dimen317
\Hy@linkcounter=\count343
\Hy@pagecounter=\count344

(c:/texlive/2024/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-01-20 v7.01h Hyperref: PDFDocEncoding definition (HO)
)
(c:/texlive/2024/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count345

(c:/texlive/2024/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-01-20 v7.01h Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `colorlinks' set `true' on input line 4062.
Package hyperref Info: Hyper figures OFF on input line 4179.
Package hyperref Info: Link nesting OFF on input line 4184.
Package hyperref Info: Hyper index ON on input line 4187.
Package hyperref Info: Plain pages OFF on input line 4194.
Package hyperref Info: Backreferencing OFF on input line 4199.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4446.
\c@Hy@tempcnt=\count346

(c:/texlive/2024/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4784.
\XeTeXLinkMargin=\dimen318

(c:/texlive/2024/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(c:/texlive/2024/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count347
\Field@Width=\dimen319
\Fld@charsize=\dimen320
Package hyperref Info: Hyper figures OFF on input line 6063.
Package hyperref Info: Link nesting OFF on input line 6068.
Package hyperref Info: Hyper index ON on input line 6071.
Package hyperref Info: backreferencing OFF on input line 6078.
Package hyperref Info: Link coloring ON on input line 6081.
Package hyperref Info: Link coloring with OCG OFF on input line 6088.
Package hyperref Info: PDF/A mode OFF on input line 6093.

(c:/texlive/2024/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count348
\c@Item=\count349
\c@Hfootnote=\count350
)
Package hyperref Info: Driver (autodetected): hluatex.

(c:/texlive/2024/texmf-dist/tex/latex/hyperref/hluatex.def
File: hluatex.def 2024-01-20 v7.01h Hyperref driver for luaTeX

(c:/texlive/2024/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
(c:/texlive/2024/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count351
\c@bookmark@seq@number=\count352

(c:/texlive/2024/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(c:/texlive/2024/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip94
)
(c:/texlive/2024/texmf-dist/tex/latex/tools/tabularx.sty
Package: tabularx 2023/07/08 v2.11c `tabularx' package (DPC)
\TX@col@width=\dimen321
\TX@old@table=\dimen322
\TX@old@col=\dimen323
\TX@target=\dimen324
\TX@delta=\dimen325
\TX@cols=\count353
\TX@ftn=\toks53
)
(c:/texlive/2024/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2021/03/15 v2.8 Span multiple rows of a table
\multirow@colwidth=\skip95
\multirow@cntb=\count354
\multirow@dima=\skip96
\bigstrutjot=\dimen326
)
(c:/texlive/2024/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count355
\float@exts=\toks54
\float@box=\box132
\@float@everytoks=\toks55
\@floatcapt=\box133
)
(c:/texlive/2024/texmf-dist/tex/latex/capt-of/capt-of.sty
Package: capt-of 2009/12/29 v0.2 standard captions outside of floats
)
\c@theorem=\count356
\c@lemma=\count357
\c@corollary=\count358
\c@proposition=\count359
\c@characterization=\count360
\c@property=\count361
\c@problem=\count362
\c@example=\count363
\c@examplesanddefinitions=\count364
\c@remark=\count365
\c@definition=\count366
\c@hypothesis=\count367
\c@notation=\count368
\c@assumption=\count369
\c@algorithm=\count370
luaotfload | cache : Lookup cache loaded from C:/texlive/2024/texmf-var/luatex-c
ache/generic/names/luaotfload-lookup-cache.luc.

Package fontspec Info: Font family 'TimesNewRoman(0)' created for font 'Times
(fontspec)             New Roman' with options [Ligatures=TeX].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"name:TimesNewRoman:mode=node;script=latn;language=df
lt;+tlig;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.:
(fontspec)             <->"name:TimesNewRoman:mode=node;script=latn;language=df
lt;+tlig;+smcp;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"name:TimesNewRoman/B:mode=node;script=latn;language=
dflt;+tlig;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.:
(fontspec)             <->"name:TimesNewRoman/B:mode=node;script=latn;language=
dflt;+tlig;+smcp;"
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->"name:TimesNewRoman/I:mode=node;script=latn;language=
dflt;+tlig;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.:
(fontspec)             <->"name:TimesNewRoman/I:mode=node;script=latn;language=
dflt;+tlig;+smcp;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.:
(fontspec)             <->"name:TimesNewRoman/BI:mode=node;script=latn;language
=dflt;+tlig;"
(fontspec)             - 'bold italic small caps'  (b/scit) with NFSS spec.:
(fontspec)             <->"name:TimesNewRoman/BI:mode=node;script=latn;language
=dflt;+tlig;+smcp;"

 (./双语.aux)
\openout1 = 双语.aux

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 292.
LaTeX Font Info:    ... okay on input line 292.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 292.
LaTeX Font Info:    ... okay on input line 292.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 292.
LaTeX Font Info:    ... okay on input line 292.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 292.
LaTeX Font Info:    ... okay on input line 292.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 292.
LaTeX Font Info:    Trying to load font information for TS1+cmr on input line 2
92.

(c:/texlive/2024/texmf-dist/tex/latex/base/ts1cmr.fd
File: ts1cmr.fd 2023/04/13 v2.5m Standard LaTeX font definitions
)
LaTeX Font Info:    ... okay on input line 292.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 292.
LaTeX Font Info:    ... okay on input line 292.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 292.
LaTeX Font Info:    ... okay on input line 292.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 292.
LaTeX Font Info:    ... okay on input line 292.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 292.
LaTeX Font Info:    ... okay on input line 292.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 292.
LaTeX Font Info:    ... okay on input line 292.

(c:/texlive/2024/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count371
\scratchdimen=\dimen327
\scratchbox=\box134
\nofMPsegments=\count372
\nofMParguments=\count373
\everyMPshowfont=\toks56
\MPscratchCnt=\count374
\MPscratchDim=\dimen328
\MPnumerator=\count375
\makeMPintoPDFobject=\count376
\everyMPtoPDFconversion=\toks57
) (c:/texlive/2024/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(c:/texlive/2024/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))
*geometry* driver: auto-detecting
*geometry* detected driver: luatex
*geometry* verbose mode - [ preamble ] result:
* driver: luatex
* paper: custom
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(56.9055pt, 483.69687pt, 56.9055pt)
* v-part:(T,H,B)=(71.13188pt, 668.63977pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=810.90353pt
* \textwidth=483.69687pt
* \textheight=668.63977pt
* \oddsidemargin=-15.36449pt
* \evensidemargin=-15.36449pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=59.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package caption Info: Begin \AtBeginDocument code.
Package caption Info: changepage package is loaded.
\caption@adjustwidth@hsize=\dimen329
\caption@adjustwidth@linewidth=\dimen330
Package caption Info: float package is loaded.
Package caption Info: hyperref package is loaded.
Package caption Info: End \AtBeginDocument code.

Package fontspec Info: Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup9
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 292.
LaTeX Font Info:    Redeclaring math accent \acute on input line 292.
LaTeX Font Info:    Redeclaring math accent \grave on input line 292.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 292.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 292.
LaTeX Font Info:    Redeclaring math accent \bar on input line 292.
LaTeX Font Info:    Redeclaring math accent \breve on input line 292.
LaTeX Font Info:    Redeclaring math accent \check on input line 292.
LaTeX Font Info:    Redeclaring math accent \hat on input line 292.
LaTeX Font Info:    Redeclaring math accent \dot on input line 292.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 292.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 292.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 292.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 292.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 292.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 292.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 292.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 292.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 292.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 292.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 292.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 292.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 292.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 292.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 292.

LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/TimesNewRoman(0)/m/n on input line 2
92.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 292.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/TimesNewRoman(0)/m/n on input line 
292.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/TimesNewRoman(0)/m/n --> TU/TimesNewRoman(0)/m/n on 
input line 292.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/TimesNewRoman(0)/m/it on input line
 292.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/TimesNewRoman(0)/b/n on input line 
292.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 292.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 292.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/TimesNewRoman(0)/m/n --> TU/TimesNewRoman(0)/b/n on 
input line 292.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/TimesNewRoman(0)/b/it on input lin
e 292.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/b/n on input line 292.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 292.
LaTeX Info: Redefining \microtypecontext on input line 292.
Package microtype Info: Applying patch `item' on input line 292.
Package microtype Info: Applying patch `toc' on input line 292.
Package microtype Info: Applying patch `eqnum' on input line 292.
Package microtype Info: Applying patch `footnote' on input line 292.
Package microtype Info: Applying patch `verbatim' on input line 292.
Package microtype Info: Generating PDF output.
Package microtype Info: Character protrusion enabled (level 2).
Package microtype Info: Using default protrusion set `alltext'.
Package microtype Info: Automatic font expansion enabled (level 2),
(microtype)             stretch: 20, shrink: 20, step: 1, non-selected.
Package microtype Info: Using default expansion set `alltext-nott'.
LaTeX Info: Redefining \showhyphens on input line 292.
Package microtype Info: No adjustment of tracking.
Package microtype Info: No adjustment of spacing.
Package microtype Info: No adjustment of kerning.
Package microtype Info: Loading generic protrusion settings for font family
(microtype)             `TimesNewRoman' (encoding: TU).
(microtype)             For optimal results, create family-specific settings.
(microtype)             See the microtype manual for details.
Package hyperref Info: Link coloring ON on input line 292.
(./双语.out) (./双语.out)
\@outlinefile=\write4

\openout4 = 双语.out
<logo.png, id=47, 3011.4909pt x 1008.6483pt>
File: logo.png Graphic file (type png)
<use logo.png>
Package luatex.def Info: logo.png  used on input line 317.
(luatex.def)             Requested size: 105.2751pt x 35.26012pt.
LaTeX Font Info:    Trying to load font information for U+msa on input line 320
.

(c:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
(c:/texlive/2024/texmf-dist/tex/latex/microtype/mt-msa.cfg
File: mt-msa.cfg 2006/02/04 v1.1 microtype config. file: AMS symbols (a) (RS)
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 320
.

(c:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
(c:/texlive/2024/texmf-dist/tex/latex/microtype/mt-msb.cfg
File: mt-msb.cfg 2005/06/01 v1.0 microtype config. file: AMS symbols (b) (RS)
)
(c:/texlive/2024/texmf-dist/tex/latex/microtype/mt-cmr.cfg
File: mt-cmr.cfg 2013/05/19 v2.2 microtype config. file: Computer Modern Roman 
(RS)
)
Overfull \hbox (1.65pt too wide) in paragraph at lines 314--334
$[]$    $[]$
 []

<ORCID.png, id=49, 26.0975pt x 24.59187pt>
File: ORCID.png Graphic file (type png)
<use ORCID.png>
Package luatex.def Info: ORCID.png  used on input line 340.
(luatex.def)             Requested size: 13.08846pt x 12.33342pt.
File: ORCID.png Graphic file (type png)
<use ORCID.png>
Package luatex.def Info: ORCID.png  used on input line 340.
(luatex.def)             Requested size: 13.08846pt x 12.33342pt.

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 20.63005pt, for example:
(fancyhdr)                \setlength{\headheight}{20.63005pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-8.63005pt}.

[1071{c:/texlive/2024/texmf-var/fonts/map/pdftex/updmap/pdftex.map}

<./logo.png><./ORCID.png>]
<image1.png, id=69, 130.4875pt x 130.4875pt>
File: image1.png Graphic file (type png)
<use image1.png>
Package luatex.def Info: image1.png  used on input line 411.
(luatex.def)             Requested size: 59.2121pt x 59.2126pt.

Underfull \hbox (badness 10000) in paragraph at lines 425--435
[][]
 []


Overfull \hbox (246.84843pt too wide) in paragraph at lines 425--435
$[]$ 
 []

<image2a.png, id=70, 100.375pt x 100.375pt>
File: image2a.png Graphic file (type png)
<use image2a.png>
Package luatex.def Info: image2a.png  used on input line 447.
(luatex.def)             Requested size: 120.92421pt x 120.93182pt.
<image2b.png, id=71, 100.375pt x 100.375pt>
File: image2b.png Graphic file (type png)
<use image2b.png>
Package luatex.def Info: image2b.png  used on input line 447.
(luatex.def)             Requested size: 120.92421pt x 120.93182pt.

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 20.63005pt, for example:
(fancyhdr)                \setlength{\headheight}{20.63005pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-8.63005pt}.

[1072<./image1.png>]

Package array Warning: Column C is already defined on input line 462.


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 20.63005pt, for example:
(fancyhdr)                \setlength{\headheight}{20.63005pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-8.63005pt}.

[1073<./image2a.png><./image2b.png>]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 20.63005pt, for example:
(fancyhdr)                \setlength{\headheight}{20.63005pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-8.63005pt}.

[1074]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 20.63005pt, for example:
(fancyhdr)                \setlength{\headheight}{20.63005pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-8.63005pt}.

[1075] (./双语.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
 ***********
Package rerunfilecheck Info: File `双语.out' has not changed.
(rerunfilecheck)             Checksum: 36F618AB547C8B97B8FC78704C044D4F;1296.
)

Here is how much of LuaTeX's memory you used:
 29845 strings out of 476447
 125086,1977958 words of node,token memory allocated
 741 words of node memory still in use:
   8 hlist, 2 vlist, 2 rule, 1 local_par, 5 glue, 5 kern, 2 penalty, 1 margin_ke
rn, 4 glyph, 13 attribute, 81 glue_spec, 13 attribute_list, 2 write nodes
   avail lists: 1:1,2:1167,3:499,4:264,5:275,6:155,7:11533,8:34,9:1222,10:20,11:
876
 51202 multiletter control sequences out of 65536+600000
 101 fonts using 26040535 bytes
 83i,15n,89p,1567b,694s stack positions out of 10000i,1000n,20000p,200000b,200000s
<c:/Windows/Fonts/timesi.ttf><c:/Windows/Fonts/timesbd.ttf><c:/Windows/Fonts/tim
es.ttf><c:/Windows/Fonts/timesbi.ttf><c:/texlive/2024/texmf-dist/fonts/type1/pub
lic/amsfonts/cm/cmmi10.pfb><c:/texlive/2024/texmf-dist/fonts/type1/public/amsfon
ts/cm/cmr10.pfb><c:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr7.
pfb>
Output written on 双语.pdf (5 pages, 709294 bytes).

PDF statistics: 157 PDF objects out of 1000 (max. 8388607)
 124 compressed objects within 2 object streams
 36 named destinations out of 1000 (max. 131072)
 96 words of extra memory for PDF output out of 10000 (max. 100000000)


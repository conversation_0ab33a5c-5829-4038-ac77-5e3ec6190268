#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
尝试打开生成的PDF文件
"""

import subprocess
import sys
from pathlib import Path
import time

def try_open_pdf(pdf_path, description):
    """尝试打开PDF文件"""
    pdf_file = Path(pdf_path)
    
    if not pdf_file.exists():
        print(f"❌ {description}: 文件不存在 - {pdf_path}")
        return False
    
    print(f"📄 {description}: {pdf_file.name} ({pdf_file.stat().st_size:,} 字节)")
    
    try:
        # 在Windows上使用默认程序打开PDF
        subprocess.run(['start', '', str(pdf_file)], shell=True, check=True)
        print(f"✅ {description}: 已尝试打开")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}: 打开失败 - {e}")
        return False
    except Exception as e:
        print(f"❌ {description}: 未知错误 - {e}")
        return False

def main():
    """主函数"""
    print("🔍 尝试打开生成的PDF文件...")
    
    # 要检查的PDF文件
    pdfs_to_check = [
        ("test_figure_fix_output/output.pdf", "最新修复版本"),
        ("test_fixed_output/output.pdf", "早期修复版本"),
        ("test_8565_double_column/output.pdf", "原始版本（对比用）")
    ]
    
    success_count = 0
    
    for pdf_path, description in pdfs_to_check:
        print(f"\n📋 检查 {description}:")
        if try_open_pdf(pdf_path, description):
            success_count += 1
        time.sleep(1)  # 等待1秒再打开下一个
    
    print(f"\n📊 总结: 成功尝试打开 {success_count}/{len(pdfs_to_check)} 个PDF文件")
    
    if success_count > 0:
        print("\n💡 提示:")
        print("- 如果PDF无法打开，请尝试使用不同的PDF阅读器")
        print("- 推荐使用: Adobe Reader, Chrome浏览器, Edge浏览器")
        print("- 检查是否安装了PDF阅读器")
    
    return success_count > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

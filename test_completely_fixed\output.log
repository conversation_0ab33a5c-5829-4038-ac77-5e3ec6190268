This is XeTeX, Version 3.141592653-2.6-0.999997 (TeX Live 2025) (preloaded format=xelatex 2025.7.23)  31 JUL 2025 12:25
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**output.tex
(./output.tex
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-07-19>
(c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/base/article.cls
Document Class: article 2025/01/22 v1.4n Standard LaTeX document class
(c:/Users/<USER>/AppData/Roaming/TinyTeX/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2025/01/22 v1.4n Standard LaTeX file (size option)
)
\c@part=\count271
\c@section=\count272
\c@subsection=\count273
\c@subsubsection=\count274
\c@paragraph=\count275
\c@subparagraph=\count276
\c@figure=\count277
\c@table=\count278
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen148
)

! LaTeX Error: File `flushend.sty' not found.

Type X to quit or <RETURN> to proceed,
or enter new name. (Default extension: sty)

Enter file name: 
! Emergency stop.
<read *> 
         
l.5 \usepackage
               {graphicx}^^M
*** (cannot \read from terminal in nonstop modes)

 
Here is how much of TeX's memory you used:
 243 strings out of 470473
 3035 string characters out of 5489164
 419660 words of memory out of 5000000
 28639 multiletter control sequences out of 15000+600000
 626833 words of font info for 41 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 35i,0n,38p,162b,40s stack positions out of 10000i,1000n,20000p,200000b,200000s
No pages of output.
